﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Text;

namespace UNI.Model.Core
{
    public class UserBase
    {
        public string userId { get; set; }
    }
    public class coreUserProfile
    {
        public string reg_id { get; set; }
        public Guid userId { get; set; }
        public string loginName { get; set; }
        public string referral_by { get; set; }
        public string avatarUrl { get; set; }
        public string fullName { get; set; }
        public string idcard_no { get; set; }
        public string phone { get; set; }
        public string email { get; set; }
        public string custId { get; set; }
        public bool idcard_verified { get; set; }
        public bool phone_verified { get; set; }
        public bool email_verified { get; set; }
        public bool ekyc_verified { get; set; }
        public bool saler_status { get; set; }
    }
    public class userProfileAvatar
    {
        public string avatarUrl { get; set; }
    }
    public class userProfileImage
    {
        public string imageUrl { get; set; }
    }
    public class userAvatar
    {
        public Guid? Oid { get; set; }
        public IFormFile formFile { get; set; }
    }
    public class UserShort
    {
        public Guid userId { get; set; }
        public string loginName { get; set; }
        public string nickName { get; set; }
        public string fullName { get; set; }
        public string phone { get; set; }
        public string email { get; set; }
        public string custId { get; set; }
    }
    
    public class userProfile
    {
        public string reg_id { get; set; }
        public Guid userId { get; set; }
        public string custId { get; set; }
        public string cif_no { get; set; }
        public string referralCd { get; set; }
        public string avatarUrl { get; set; }
        public string loginName { get; set; }
        public string tax_code { get; set; }
        public string nickName { get; set; }
        public string fullName { get; set; }
        public string cntry_Reg { get; set; }
        public string phoneF { get; set; }
        public string phone { get; set; }
        public string email { get; set; }
        public string sex { get; set; }
        public string birthday { get; set; }        
        public string res_Add { get; set; }
        public string res_City { get; set; }
        public string res_Cntry { get; set; }
        public string trad_Add { get; set; }
        public int idcard_type { get; set; }
        public string idcard_No { get; set; }
        public string idcard_Issue_Dt { get; set; }
        public string idcard_Issue_Plc { get; set; }
        public string idcard_expire_dt { get; set; }
        public int idcard_Verified { get; set; }
        public int email_Verified { get; set; }
        public bool agreed_St { get; set; }
        public string agreed_Dt { get; set; }
        public string created_Dt { get; set; }
        public string modified_Dt { get; set; }
        public bool lock_St { get; set; }
        public string lock_Dt { get; set; }
        public bool last_St { get; set; }
        public string last_Dt { get; set; }
        public bool core_linked { get; set; }
        public bool residen_linked { get; set; }
        public bool staff_linked { get; set; }
        public bool fb_linked { get; set; }
        public string fb_id { get; set; }
        public string fb_name { get; set; }
        public string referral_by { get; set; }
        public string referral_fullName { get; set; }
        public string qrCode { get; set; }
        public int verifyType { get; set; }
        public string org_name { get; set; }
        public string origin_add { get; set; }
        public int? phone_verified { get; set; }
    }
    public class UserProfileFull
    {
        public Guid userId { get; set; }
        public string loginName { get; set; }
        public string avatarUrl { get; set; }
        public string nickName { get; set; }
        public string fullName { get; set; }
        public string phone { get; set; }
        public string email { get; set; }
        public string sex { get; set; }
        public string birthday { get; set; }
        public string res_Add { get; set; }
        public string res_Cntry { get; set; }
        public string trad_Add { get; set; }

        public bool email_Verified { get; set; }
        public bool idcard_Verified { get; set; }
        public bool agreed_St { get; set; }

        public string referralCd { get; set; }
        public bool core_linked { get; set; }
        public bool residen_linked { get; set; }
        public bool staff_linked { get; set; }
        public bool fb_linked { get; set; }
        public bool gg_linked { get; set; }
        public bool invited_st { get; set; }
        public bool sale_st { get; set; }
        public int loginType { get; set; }
        public List<viewField> fields { get; set; }
        public corePoint point { get; set; }
        public List<coreUserProfileMeta> metas { get; set; }
        public bool isUserCms { get; set; }
        public bool isUserNoble { get; set; }
        public string messageNoble { get; set; }

    }
        
    public class userProfileUpgrate
    {
        public int userType { get; set; }
        public string userId { get; set; }
        public string tax_code { get; set; }
        public string fullName { get; set; }
        public string phone { get; set; }
        public string email { get; set; }
        //public string sex { get; set; }
        //public string birthday { get; set; }
        public string res_Add { get; set; }
        public string res_City { get; set; }
        public string res_Cntry { get; set; }
        public string trad_Add { get; set; }

    }
    public class corePoint
    {
        public string referralCd { get; set; }
        public decimal cur_bal_point { get; set; }
        public int last_dt { get; set; }
        public int u_rank { get; set; }
        public int gr_rank { get; set; }
    }
}
