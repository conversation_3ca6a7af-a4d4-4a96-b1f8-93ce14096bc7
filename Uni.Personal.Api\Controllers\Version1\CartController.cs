using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using UNI.Model;
using UNI.Model.Api;
using Uni.Personal.BLL.Interfaces;
using Uni.Personal.Model;

namespace Uni.Personal.Api.Controllers.Version1
{
    [Route("/api/v1/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class CartController : UniController
    {
        private readonly ICartService _cartService;

        public CartController(ICartService cartService, ILoggerFactory logger) : base(logger)
        {
            _cartService = cartService;
        }

        /// <summary>
        /// Get paginated list of cart items
        /// </summary>
        /// <param name="query">Filter criteria for cart items</param>
        /// <returns>Paginated list of cart items</returns>
        [HttpGet]
        public async Task<BaseResponse<CommonListPage>> GetPage([FromQuery] CartFilterInput? query)
        {
            if (query == null)
            {
                return GetErrorResponse<CommonListPage>(ApiResult.Error, 12, "Invalid request data");
            }
            var result = await _cartService.GetPageAsync(query);
            return GetResponse(ApiResult.Success, result);
        }

        /// <summary>
        /// Get cart information by ID
        /// </summary>
        /// <param name="oid">Cart ID</param>
        /// <returns>Cart information</returns>
        [HttpGet]
        public async Task<BaseResponse<CommonViewOidInfo>> GetInfo([FromQuery] Guid? oid)
        {
            var result = await _cartService.GetInfoAsync(oid);
            return GetResponse(ApiResult.Success, result);
        }

        /// <summary>
        /// Create or update cart
        /// </summary>
        /// <param name="info">Cart information</param>
        /// <returns>Validation result with cart ID</returns>
        [HttpPost]
        public async Task<BaseResponse<BaseValidate<Guid?>>> SetInfo([FromBody] CommonViewOidInfo? info)
        {
            if (info == null)
            {
                return GetErrorResponse<BaseValidate<Guid?>>(ApiResult.Error, 12, "Invalid request data");
            }

            var result = await _cartService.SetInfoAsync(info);

            return result.valid ? GetResponse(ApiResult.Success, result) : GetResponse(ApiResult.Error, result, result.messages);
        }

        /// <summary>
        /// Delete cart by ID
        /// </summary>
        /// <param name="oid">Cart ID</param>
        /// <returns>Deletion result</returns>
        [HttpDelete]
        public async Task<BaseResponse<BaseValidate<bool>>> Delete([FromQuery] Guid? oid)
        {
            if (!oid.HasValue)
            {
                return GetErrorResponse<BaseValidate<bool>>(ApiResult.Error, 12, "Cart ID is required");
            }

            var result = await _cartService.DeleteAsync(oid);
            return result.valid ? GetResponse(ApiResult.Success, result) : GetResponse(ApiResult.Error, result, result.messages);
        }
    }
}
