using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using UNI.Model;
using UNI.Model.Api;
using Uni.Personal.BLL.Interfaces;
using Uni.Personal.Model;

namespace Uni.Personal.Api.Controllers.Version1
{
    [Route("/api/v1/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class CartController : UniController
    {
        private readonly ICartService _cartService;

        public CartController(ICartService cartService, ILoggerFactory logger) : base(logger)
        {
            _cartService = cartService;
        }

        [HttpGet]
        public async Task<BaseResponse<CommonListPage>> GetPage([FromQuery] CartFilterInput? query)
        {
            throw new NotImplementedException();
        }

        [HttpGet]
        public async Task<BaseResponse<CommonViewOidInfo>> GetInfo([FromQuery] Guid? oid)
        {
            throw new NotImplementedException();
        }

        [HttpPost]
        public async Task<BaseResponse<BaseValidate<Guid?>>> SetInfo([FromBody] CommonViewOidInfo? info)
        {
            throw new NotImplementedException();
        }

        [HttpDelete]
        public async Task<BaseResponse<BaseValidate<bool>>> Delete([FromQuery] Guid? oid)
        {
            throw new NotImplementedException();
        }
    }
}
