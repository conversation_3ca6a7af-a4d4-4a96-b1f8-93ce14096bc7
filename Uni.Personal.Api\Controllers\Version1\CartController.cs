using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using UNI.Model;
using UNI.Model.Api;
using Uni.Personal.BLL.Interfaces;
using Uni.Personal.Model;

namespace Uni.Personal.Api.Controllers.Version1
{
    [Route("/api/v1/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class CartController : UniController
    {
        private readonly ICartService _cartService;

        public CartController(ICartService cartService, ILoggerFactory logger) : base(logger)
        {
            _cartService = cartService;
        }

        /// <summary>
        /// Get list of cart items
        /// </summary>
        /// <param name="filter">Filter criteria for cart items</param>
        /// <returns>List of cart items</returns>
        [HttpGet]
        public async Task<BaseResponse<IEnumerable<CartItem>>> List([FromQuery] CartFilterInput? filter)
        {
            if (filter == null)
            {
                return GetErrorResponse<IEnumerable<CartItem>>(ApiResult.Error, 12, "Invalid request data");
            }

            var result = await _cartService.GetCartItemsAsync(filter);
            return GetResponse(ApiResult.Success, result);
        }

        /// <summary>
        /// Add or update cart item
        /// </summary>
        /// <param name="cartItem">Cart item to add or update</param>
        /// <returns>Validation result with cart item ID</returns>
        [HttpPost]
        public async Task<BaseResponse<CartItemInput>> Set([FromBody] CartItemInput? cartItem)
        {
            if (cartItem == null)
            {
                return GetErrorResponse<CartItemInput>(ApiResult.Error, 12, "Invalid request data");
            }

            var result = await _cartService.SetCartItemAsync(cartItem);
            if (result.valid)
            {
                cartItem.Id = result.id;
            }
            return result.valid ? GetResponse(ApiResult.Success, cartItem, result.messages) : GetResponse(ApiResult.Error, cartItem, result.messages);
        }

        /// <summary>
        /// Remove cart item
        /// </summary>
        /// <param name="id">Cart item ID to remove</param>
        /// <returns>Validation result</returns>
        [HttpDelete]
        public async Task<BaseResponse<string>> Remove([FromQuery] Guid id)
        {
            var result = await _cartService.RemoveCartItemAsync(id);
            return result.valid ? GetResponse(ApiResult.Success, string.Empty, result.messages) : GetResponse(ApiResult.Error, string.Empty, result.messages);
        }
    }
}
