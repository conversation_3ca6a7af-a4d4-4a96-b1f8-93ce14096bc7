namespace UNI.Utilities.ExternalStorage.Models
{
    /// <summary>
    /// Enumeration of supported storage provider types
    /// </summary>
    public enum StorageProviderType
    {
        /// <summary>
        /// MinIO object storage
        /// </summary>
        MinIO,

        /// <summary>
        /// Amazon S3
        /// </summary>
        AwsS3,

        /// <summary>
        /// Firebase Storage
        /// </summary>
        Firebase,

        /// <summary>
        /// Azure Blob Storage
        /// </summary>
        AzureBlob,

        /// <summary>
        /// Google Cloud Storage
        /// </summary>
        GoogleCloud
    }

    /// <summary>
    /// Information about a storage bucket/container
    /// </summary>
    public class BucketInfo
    {
        /// <summary>
        /// Name of the bucket/container
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Creation date of the bucket/container
        /// </summary>
        public DateTime CreationDate { get; set; }

        /// <summary>
        /// Region where the bucket/container is located
        /// </summary>
        public string? Region { get; set; }

        /// <summary>
        /// Additional metadata about the bucket/container
        /// </summary>
        public Dictionary<string, string> Metadata { get; set; } = new();
    }

    /// <summary>
    /// Result of an upload operation
    /// </summary>
    public class StorageUploadResult
    {
        /// <summary>
        /// Name of the bucket/container
        /// </summary>
        public string BucketName { get; set; } = string.Empty;

        /// <summary>
        /// Name of the uploaded object
        /// </summary>
        public string ObjectName { get; set; } = string.Empty;

        /// <summary>
        /// Size of the uploaded object in bytes
        /// </summary>
        public long Size { get; set; }

        /// <summary>
        /// ETag or hash of the uploaded object
        /// </summary>
        public string? ETag { get; set; }

        /// <summary>
        /// Content type of the uploaded object
        /// </summary>
        public string? ContentType { get; set; }

        /// <summary>
        /// Upload completion timestamp
        /// </summary>
        public DateTime UploadedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Additional metadata about the upload
        /// </summary>
        public Dictionary<string, string> Metadata { get; set; } = new();

        /// <summary>
        /// Provider-specific information
        /// </summary>
        public Dictionary<string, object> ProviderData { get; set; } = new();
        /// <summary>
        /// File path
        /// </summary>
        /// <value></value>
        public string? FilePath { get; set; }
    }

    /// <summary>
    /// Progress information for upload operations
    /// </summary>
    public class StorageUploadProgress
    {
        /// <summary>
        /// Total bytes to upload
        /// </summary>
        public long TotalBytes { get; set; }

        /// <summary>
        /// Bytes uploaded so far
        /// </summary>
        public long UploadedBytes { get; set; }

        /// <summary>
        /// Upload progress percentage (0-100)
        /// </summary>
        public double ProgressPercentage => TotalBytes > 0 ? (double)UploadedBytes / TotalBytes * 100 : 0;

        /// <summary>
        /// Current part being uploaded (for multipart uploads)
        /// </summary>
        public int? CurrentPart { get; set; }

        /// <summary>
        /// Total number of parts (for multipart uploads)
        /// </summary>
        public int? TotalParts { get; set; }

        /// <summary>
        /// Upload speed in bytes per second
        /// </summary>
        public long? BytesPerSecond { get; set; }

        /// <summary>
        /// Estimated time remaining
        /// </summary>
        public TimeSpan? EstimatedTimeRemaining { get; set; }
    }

    /// <summary>
    /// Information about a storage object
    /// </summary>
    public class StorageObjectInfo
    {
        /// <summary>
        /// Name of the object
        /// </summary>
        public string ObjectName { get; set; } = string.Empty;

        /// <summary>
        /// Size of the object in bytes
        /// </summary>
        public long Size { get; set; }

        /// <summary>
        /// Last modified date of the object
        /// </summary>
        public DateTime LastModified { get; set; }

        /// <summary>
        /// ETag or hash of the object
        /// </summary>
        public string? ETag { get; set; }

        /// <summary>
        /// Content type of the object
        /// </summary>
        public string? ContentType { get; set; }

        /// <summary>
        /// Storage class of the object
        /// </summary>
        public string? StorageClass { get; set; }

        /// <summary>
        /// Whether the object is a directory/folder
        /// </summary>
        public bool IsDirectory { get; set; }

        /// <summary>
        /// Additional metadata about the object
        /// </summary>
        public Dictionary<string, string> Metadata { get; set; } = new();

        /// <summary>
        /// Provider-specific information
        /// </summary>
        public Dictionary<string, object> ProviderData { get; set; } = new();
        /// <summary>
        /// File path
        /// </summary>
        /// <value></value>
        public string? FilePath { get; set; }
    }

    /// <summary>
    /// Result of a delete operation
    /// </summary>
    public class StorageDeleteResult
    {
        /// <summary>
        /// Name of the object that was deleted
        /// </summary>
        public string ObjectName { get; set; } = string.Empty;

        /// <summary>
        /// Whether the deletion was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Error message if deletion failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Error code if deletion failed
        /// </summary>
        public string? ErrorCode { get; set; }
    }

    /// <summary>
    /// Base configuration settings for storage providers
    /// </summary>
    public abstract class StorageSettings
    {
        /// <summary>
        /// Storage provider type
        /// </summary>
        public abstract StorageProviderType ProviderType { get; }

        /// <summary>
        /// Whether to create bucket if it doesn't exist
        /// </summary>
        public bool CreateBucketIfNotExists { get; set; } = false;

        /// <summary>
        /// Default bucket name to use if not specified in operations
        /// </summary>
        public string? DefaultBucket { get; set; }

        /// <summary>
        /// Connection timeout in seconds
        /// </summary>
        public int TimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// Additional provider-specific settings
        /// </summary>
        public Dictionary<string, object> AdditionalSettings { get; set; } = new();
    }

    /// <summary>
    /// Configuration for external storage service
    /// </summary>
    public class ExternalStorageConfiguration
    {
        /// <summary>
        /// Default storage provider to use
        /// </summary>
        public StorageProviderType DefaultProvider { get; set; } = StorageProviderType.MinIO;

        /// <summary>
        /// MinIO storage settings
        /// </summary>
        public MinIOStorageSettings? MinIO { get; set; }

        /// <summary>
        /// AWS S3 storage settings
        /// </summary>
        public AwsS3StorageSettings? AwsS3 { get; set; }

        /// <summary>
        /// Firebase storage settings
        /// </summary>
        public FirebaseStorageSettings? Firebase { get; set; }

        /// <summary>
        /// Whether to enable fallback to other providers if primary fails
        /// </summary>
        public bool EnableFallback { get; set; } = false;

        /// <summary>
        /// Fallback provider order
        /// </summary>
        public List<StorageProviderType> FallbackOrder { get; set; } = new();
    }
}
