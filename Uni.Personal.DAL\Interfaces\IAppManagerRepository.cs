﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UNI.Model;
using UNI.Model.APPM;
using UNI.Model.Firestore;

namespace Uni.Personal.DAL.Interfaces
{

    public interface IAppManagerRepository
    {

        #region message-reg
        //Task<int> TakeMessage(BaseCtrlClient clt, MessageBase mess);
        #endregion message-reg

        #region token-reg
        //string GetUserToken(BaseCtrlClient clt, int tokenType, int mode = 0);
        //userTokenMode GetUserByToken(string token, int tokenType);
        Task<OtpMessageGet> TakeOTP(BaseCtrlClient clt, WalUserGrant registed);
        Task<ResponseCode> SetVerificationCode(string userId, userVerification code);
        Task<BaseValidate> SetOtpStatus(string userId, userOtpStatus status);
        #endregion token-reg

        //Task<int> InsetSmartDeviceProfile(SmartDeviceSet deviceprofile);

        //#region thread-reg
        //ResponseList<List<fbThreadList>> GetThreadListByUser(FilterBase filter);
        //Task<fbThread> SetThreadFetch(string userId, fbThreadSet thread);
        //Task<fbThread> SetThreadInfo(string userId, fbThread thread);
        //fbThread GetThread(string userId, Guid id);
        //Task<BaseValidate> SetThreadInvite(string userId, Guid? id, string role, List<fbThreadUser> user);

        //Task<BaseValidate> DelThreadInvite(Guid? thread_id, string userId);
        //Task<BaseValidate> SetThreadUser(string userId, fbThreadUserAdd id, fbThreadUser user);
        //Task<BaseValidate> DelThreadUser(string userId, fbThreadUserAdd user);
        //fbThreadUser GetUserApp(string userId);
        //#endregion thread-reg
    }
}
