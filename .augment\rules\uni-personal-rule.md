---
type: "agent_requested"
description: "Example description"
---
- BLL: store interfaces in the "Uni.Personal.DLL/Interfaces" and implemention classes in the "Uni.Personal.DLL/Services" folder. Service does not inherit any class
- DAL: store interfaces in the "Uni.Personal.DAL/Interfaces" and  implemention classes in the "Uni.Personal.DAL/Repositories" folder. Repository inherit from UniBaseRepository
- Models: Use the Uni.Personal.Model project. Organize models into folders by feature (e.g., OrderModels/OrderItem.cs).
All services must implement the following 4 methods when I ask for CRUD xxx function/ full actions:
  + GetPageAsync(xxxFilterInput query) : xxxFilterInput must inherit from FilterInput. Do not add any extra properties except those required.
  + GetInfoAsync(Guid? oid): returns CommonViewOidInfo
  + SetInfoAsync(CommonViewOidInfo info) : Used for create or update
  + DeleteAsync(Guid? oid)
- Don't use try catch blocks
- Database project in Database/dbUniMaster (MSSQL)
- Stored procedure start with sp_personal_[function]_[action]
- Always provide best practice solution
- Advise better way to do for some functions that need performance. Such as payment, order, inventory,...(Eg. some flow need MQ, cache,...)
- Create basic empty controller, BLL, DAL when asked