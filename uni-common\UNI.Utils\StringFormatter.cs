﻿using Markdig;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Xml;
using System.Xml.Serialization;
using ReverseMarkdown;

namespace UNI.Utils
{
    public class StringFormatter
    {
        public static string Indent = "    ";

        public static string PrettyPrintJson(string input)
        {
            var output = new StringBuilder(input.Length * 2);
            char? quote = null;
            int depth = 0;

            for (int i = 0; i < input.Length; ++i)
            {
                char ch = input[i];

                switch (ch)
                {
                    case '{':
                    case '[':
                        output.Append(ch);
                        if (!quote.HasValue)
                        {
                            output.AppendLine();
                            output.Append(Indent.Repeat(++depth));
                        }

                        break;
                    case '}':
                    case ']':
                        if (quote.HasValue)
                            output.Append(ch);
                        else
                        {
                            output.AppendLine();
                            output.Append(Indent.Repeat(--depth));
                            output.Append(ch);
                        }

                        break;
                    case '"':
                    case '\'':
                        output.Append(ch);
                        if (quote.HasValue)
                        {
                            if (!output.IsEscaped(i))
                                quote = null;
                        }
                        else quote = ch;

                        break;
                    case ',':
                        output.Append(ch);
                        if (!quote.HasValue)
                        {
                            output.AppendLine();
                            output.Append(Indent.Repeat(depth));
                        }

                        break;
                    case ':':
                        if (quote.HasValue) output.Append(ch);
                        else output.Append(" : ");
                        break;
                    default:
                        if (quote.HasValue || !char.IsWhiteSpace(ch))
                            output.Append(ch);
                        break;
                }
            }

            return output.ToString();
        }

        public static string RemoveVietnameseTone(string text)
        {
            string result = text.ToLower();
            result = Regex.Replace(result, "à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ|/g", "a", RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
            result = Regex.Replace(result, "è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ|/g", "e", RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
            result = Regex.Replace(result, "ì|í|ị|ỉ|ĩ|/g", "i", RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
            result = Regex.Replace(result, "ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ|/g", "o", RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
            result = Regex.Replace(result, "ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ|/g", "u", RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
            result = Regex.Replace(result, "ỳ|ý|ỵ|ỷ|ỹ|/g", "y", RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
            result = Regex.Replace(result, "đ", "d", RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
            return result;
        }

        public static string MD5Hash(string input)
        {
            StringBuilder hash = new StringBuilder();
            MD5CryptoServiceProvider md5provider = new MD5CryptoServiceProvider();
            byte[] bytes = md5provider.ComputeHash(new UTF8Encoding().GetBytes(input));

            for (int i = 0; i < bytes.Length; i++)
            {
                hash.Append(bytes[i].ToString("x2"));
            }

            return hash.ToString();
        }

        public static string RemoveWhitespace(string input)
        {
            return new string(input.ToCharArray()
                .Where(c => !Char.IsWhiteSpace(c))
                .ToArray());
        }

        //public static String PrettyPrintXml(String XML)
        //{
        //    String Result = "";

        //    MemoryStream MS = new MemoryStream();
        //    XmlTextWriter W = new XmlTextWriter(MS, Encoding.Unicode);
        //    XmlDocument D = new XmlDocument();

        //    try
        //    {
        //        // Load the XmlDocument with the XML.
        //        D.LoadXml(XML);

        //        //W.Formatting = System.Xml.Formatting.Indented;

        //        // Write the XML into a formatting XmlTextWriter
        //        D.WriteContentTo(W);
        //        W.Flush();
        //        MS.Flush();

        //        // Have to rewind the MemoryStream in order to read
        //        // its contents.
        //        MS.Position = 0;

        //        // Read MemoryStream contents into a StreamReader.
        //        StreamReader SR = new StreamReader(MS);

        //        // Extract the text from the StreamReader.
        //        String FormattedXML = SR.ReadToEnd();

        //        Result = FormattedXML;
        //    }
        //    catch (XmlException)
        //    {
        //    }

        //    MS.Dispose();
        //    //W.Close();

        //    return Result;
        //}
    }

    public static class stringExt
    {
        public static string concat_ws(this string s, params string[] input)
        {
            string res = "";
            int has = 0;
            var l = input.Length;
            for (int i = 0; i < l; i += 2)
            {
                var cur = input[i]?.Trim();
                var next = (i < l - 1) ? input[i + 1]?.Trim() : null;
                var curHas = !string.IsNullOrEmpty(cur) && !res.Contains(cur);
                var nextHas = !string.IsNullOrEmpty(next) && !res.Contains(next) && cur?.Contains(next ?? "") != true;
                if (curHas || nextHas)
                {
                    has++;
                    if (has > 1) res += s;
                }

                if (nextHas && curHas)
                    res += cur + s + next;
                else
                {
                    if (curHas)
                        res += cur;
                    if (nextHas)
                        res += next;
                }
            }

            return res;
        }

        public static string Repeat(this string str, int count)
        {
            return new StringBuilder().Insert(0, str, count).ToString();
        }

        public static bool IsEscaped(this string str, int index)
        {
            bool escaped = false;
            while (index > 0 && str[--index] == '\\') escaped = !escaped;
            return escaped;
        }

        public static bool IsEscaped(this StringBuilder str, int index)
        {
            return str.ToString().IsEscaped(index);
        }

        public static string RemoveNonPrintChars(this string src)
        {
            return Regex.Replace(src, "[^ -~]+", "", RegexOptions.None, TimeSpan.FromMilliseconds(3000));
        }

        public static string ConvertStringToNameAscii(string unicode)
        {
            unicode = Regex.Replace(unicode, "[á|à|ả|ã|ạ|â|ă|ấ|ầ|ẩ|ẫ|ậ|ắ|ằ|ẳ|ẵ|ặ]", "a", RegexOptions.IgnoreCase,TimeSpan.FromMilliseconds(3000));
            unicode = Regex.Replace(unicode, "[é|è|ẻ|ẽ|ẹ|ê|ế|ề|ể|ễ|ệ]", "e", RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
            unicode = Regex.Replace(unicode, "[ú|ù|ủ|ũ|ụ|ư|ứ|ừ|ử|ữ|ự]", "u", RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
            unicode = Regex.Replace(unicode, "[í|ì|ỉ|ĩ|ị]", "i", RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
            unicode = Regex.Replace(unicode, "[ó|ò|ỏ|õ|ọ|ô|ơ|ố|ồ|ổ|ỗ|ộ|ớ|ờ|ở|ỡ|ợ]", "o", RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
            unicode = Regex.Replace(unicode, "[đ|Đ]", "d", RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
            unicode = Regex.Replace(unicode, "[ý|ỳ|ỷ|ỹ|ỵ|Ý|Ỳ|Ỷ|Ỹ|Ỵ]", "y", RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
            unicode = Regex.Replace(unicode, "[,|~|@|/|.|:|?|#|$|%|&|*|(|)|+|”|“|'|\"|!|`|–]", "", RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));

            unicode = Regex.Replace(unicode, "[,|~|@|/|.|:|?|#|$|%|&|*|(|)|+|”|“|'|\"|!|`|–| |]", "-",
                RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));
            unicode = Regex.Replace(unicode, @"-+-", "-", RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(3000));

            return unicode;
        }
    }

    public static class XmlHelper
    {
        public static T DeserializeFromXml<T>(string xml)
        {
            var serializer = new XmlSerializer(typeof(T));
            using var reader = new StringReader(xml);
            return (T)serializer.Deserialize(reader);
        }

        public static string SerializeToXml<T>(T obj)
        {
            // Create XmlWriter with UTF-8 encoding
            var xmlWriterSettings = new XmlWriterSettings
            {
                Encoding = Encoding.UTF8,
                Indent = true // Optional: for pretty formatting
            };
            if (obj is ExpandoObject)
            {
                var dict = (IDictionary<string, object>)obj;
                var xmlDocument = new XmlDocument();
                var xmlElement = xmlDocument.CreateElement("ExpandoObject");

                foreach (var kvp in dict)
                {
                    var value = kvp.Value?.ToString() ?? string.Empty;
                    if (value == string.Empty)
                    {
                        continue;
                    }
                    var element = xmlDocument.CreateElement(kvp.Key);
                    element.InnerText = value;
                    xmlElement.AppendChild(element);
                }

                xmlDocument.AppendChild(xmlElement);
                using var stringWriter = new StringWriter();
                using var xmlTextWriter = XmlWriter.Create(stringWriter, xmlWriterSettings);
                xmlDocument.WriteTo(xmlTextWriter);
                xmlTextWriter.Flush();
                return stringWriter.GetStringBuilder().ToString();
            }

            var serializer = new XmlSerializer(typeof(T));
            // Use StringBuilder for efficient string manipulation
            var sb = new StringBuilder();
            // Create StringWriter with UTF-8 encoding
            using (var writer = new StringWriter(sb))
            {
                using (var xmlWriter = XmlWriter.Create(writer, xmlWriterSettings))
                {
                    // Serialize the object
                    serializer.Serialize(xmlWriter, obj);
                }
            }

            return sb.ToString();
        }
    }
    public static class MarkdownHtmlHelper
    {
        public static string ConvertMarkdownToHtml(string markdown)
        {
            if (string.IsNullOrEmpty(markdown))
            {
                return string.Empty;
            }

            // Sử dụng Markdig để chuyển đổi
            var pipeline = new MarkdownPipelineBuilder().UseAdvancedExtensions().Build();
            // dùng lệnh trên nó chuyển cả mã {xâu thanh thế}
            string html = Markdown.ToHtml(markdown);

            return html;
        }
        public static string ConvertHtmlToMarkdown(string html)
        {
            if (string.IsNullOrEmpty(html))
            {
                return string.Empty;
            }

            // Cấu hình ReverseMarkdown converter
            var config = new ReverseMarkdown.Config
            {
                GithubFlavored = true, // Sử dụng cú pháp Markdown kiểu GitHub
                UnknownTags = Config.UnknownTagsOption.PassThrough, // Chuyển qua các thẻ không xác định
                RemoveComments = true // Xóa các comment trong HTML
            };

            var converter = new ReverseMarkdown.Converter(config);
            string markdown = converter.Convert(html);

            return markdown;
        }
    }
}