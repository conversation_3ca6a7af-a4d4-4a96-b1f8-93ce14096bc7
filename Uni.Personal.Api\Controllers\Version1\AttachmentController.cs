using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using UNI.Model;
using UNI.Model.Api;
using Uni.Personal.BLL.Interfaces;
using Uni.Personal.Model;
using System.ComponentModel.DataAnnotations;
using Uni.Personal.Model.AttachmentModels;

namespace Uni.Personal.Api.Controllers.Version1
{
    [Route("/api/v1/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class AttachmentController : UniController
    {
        private readonly IAttachmentService _attachmentService;

        public AttachmentController(IAttachmentService attachmentService
            , ILoggerFactory logger) : base (logger) 
        {
            _attachmentService = attachmentService;
        }

        [HttpGet]
        public async Task<BaseResponse<IEnumerable<AttachmentInfo>>> GetAttachments([FromQuery, Required] Guid? groupField)
        {
            if (!groupField.HasValue)
            {
                return GetErrorResponse<IEnumerable<AttachmentInfo>>(ApiResult.Error, 12, "Attachment ID is required");
            }

            var result = await _attachmentService.GetAttachmentsAsync(groupField);
            return GetResponse(ApiResult.Success, result);
        }
        [HttpPost]
        public async Task<BaseResponse<AttachmentSet?>> SetAttachments([FromBody] AttachmentSet attachment)
        {
            if (attachment == null)
            {
                return GetErrorResponse<AttachmentSet?>(ApiResult.Error, 12, "Attachment is required");
            }

            var result = await _attachmentService.SetAttachmentsAsync(attachment);
            return GetResponse(ApiResult.Success, result);
        }
    }
}
