using UNI.Model;

namespace Uni.Personal.Model
{
    /// <summary>
    /// Filter input for Cart pagination
    /// </summary>
    public class CartFilterInput : FilterInput
    {
        public string? ProductName { get; set; }
        public string? CustomerName { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int? Status { get; set; }
    }
}
