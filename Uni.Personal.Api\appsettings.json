{
  //"Logging": {
  //  "LogLevel": {
  //    "Default": "Information",
  //    "Microsoft.AspNetCore": "Warning"
  //  }
  //},
  "Serilog": {
    "MinimumLevel": {
      "Default": "Information",
      // "Override": {
      //  "Microsoft": "Warning",
      //  "System": "Error"
      // }
    },
    "WriteTo": [
      {
        // Wrap sinks with the Async sink to enable asynchronous logging.
        "Name": "Async",
        "Args": {
          "configure": [
            {
              // Asynchronously log to the Console.
              "Name": "Console",
              "Args": {
                // Console sink configuration
                "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss} [{Level:u3}] [{Application}/{Server}] {Message:lj}{NewLine}{Exception}"
              }
            },
            {
              // Asynchronously log to a file with rolling, retention, and file size limit settings.
              "Name": "File",
              "Args": {
                // File sink configuration
                "path": "logs/log-.txt",
                "rollingInterval": "Day",
                "retainedFileCountLimit": 30,
                "fileSizeLimitBytes": 10485760, // 10 MB per file (10 * 1024 * 1024 bytes)
                "rollOnFileSizeLimit": true,
                "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss} [{Level:u3}] [{Application}/{Server}] {Message:lj}{NewLine}{Exception}"
              }
            }
          ]
        }
      }
    ],
    "Properties": {
      "Application": "Uni Personal Portal"
    }
  },
  "Jwt": {
    "Authority": "https://idp.unicloudgroup.com.vn/realms/realm_unipersonal",
    "Audience": "unipersonal",
    "RequireHttpsMetadata": true,
    "ValidateAudience": true,
    "ValidateIssuer": true,
    "ValidateLifetime": true
  },
  "Swagger": {
    "OidcClientId": "unipersonal-swagger",
    "OidcClientSecret": "",
    "OidcScopes": ["openid", "profile", "email"]
  },
  "AllowedHosts": "*",
  "Cache": {
    "ConnectionString": "localhost:6379",
    "DefaultExpirationMinutes": 60,
    "KeyPrefix": "uni_personal:",
    "EnableCompression": false,
    "Database": 0,
    "ConnectTimeoutSeconds": 30,
    "CommandTimeoutSeconds": 30,
    "EnableRetry": true,
    "MaxRetryAttempts": 3
  }
}
