using UNI.Model;
using Uni.Personal.Model;

namespace Uni.Personal.DAL.Interfaces
{
    /// <summary>
    /// Cart repository interface
    /// </summary>
    public interface ICartRepository
    {
        /// <summary>
        /// Get paginated list of cart items
        /// </summary>
        /// <param name="filter">Filter input for cart items</param>
        /// <returns>Paginated list of cart items</returns>
        Task<CommonListPage> GetPageAsync(CartFilterInput? filter);

        /// <summary>
        /// Get cart information by ID
        /// </summary>
        /// <param name="oid">Cart ID</param>
        /// <returns>Cart information</returns>
        Task<CommonViewOidInfo> GetInfoAsync(Guid? oid);

        /// <summary>
        /// Create or update cart
        /// </summary>
        /// <param name="info">Cart information</param>
        /// <returns>Validation result with cart ID</returns>
        Task<BaseValidate<Guid?>> SetInfoAsync(CommonViewOidInfo info);

        /// <summary>
        /// Delete cart
        /// </summary>
        /// <param name="oid">Cart ID</param>
        /// <returns>Validation result</returns>
        Task<BaseValidate<bool>> DeleteAsync(Guid? oid);
    }
}
