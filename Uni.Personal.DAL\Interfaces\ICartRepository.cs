using UNI.Model;
using Uni.Personal.Model;

namespace Uni.Personal.DAL.Interfaces
{
    /// <summary>
    /// Cart repository interface
    /// </summary>
    public interface ICartRepository
    {
        /// <summary>
        /// Get list of cart items
        /// </summary>
        /// <param name="filter">Filter criteria for cart items</param>
        /// <returns>List of cart items</returns>
        Task<IEnumerable<CartItem>> GetCartItemsAsync(CartFilterInput filter);

        /// <summary>
        /// Add or update cart item
        /// </summary>
        /// <param name="cartItem">Cart item to add or update</param>
        /// <returns>Validation result with cart item ID</returns>
        Task<BaseValidate> SetCartItemAsync(CartItemInput cartItem);

        /// <summary>
        /// Remove cart item
        /// </summary>
        /// <param name="cartItemId">Cart item ID to remove</param>
        /// <returns>Validation result</returns>
        Task<BaseValidate> RemoveCartItemAsync(Guid cartItemId);
    }
}
