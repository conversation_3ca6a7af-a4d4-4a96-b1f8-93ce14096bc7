using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using UNI.Model;
using UNI.Model.Api;
using UNI.Utilities.ExternalStorage.Abstractions;
using UNI.Utilities.ExternalStorage.Models;

namespace Uni.Personal.Api.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class StorageController : UniController
    {
        private readonly IStorageService _storageService;
        private readonly ILogger _logger;
        /// <summary>
        /// ctor
        /// </summary>
        /// <param name="storageService"></param>
        /// <param name="logger"></param>
        public StorageController(IStorageService storageService, ILoggerFactory logger) : base (logger)
        {
            _storageService = storageService;
            _logger = logger.CreateLogger(GetType().Name);
        }

        [HttpGet]
        [Route("GetFile")]
        public async Task<IActionResult> GetFile(string path, string action = "default", ImageSizeEnum? size = null)
        {
            if (path == null) return new BadRequestObjectResult("path not found");
            if (path.StartsWith("http"))
            {
                return action == "url" ? Ok(path) : Redirect(path);
            }

            if (path.StartsWith("minio://") == false)
                return new BadRequestObjectResult("Scheme not supported");

            try
            {
                // Parse minio:// path to extract bucket and object name
                // Format: minio://bucket/object/path
                var uri = new Uri(path);
                var bucketName = uri.Host;
                var objectName = uri.AbsolutePath.TrimStart('/');

                if (string.IsNullOrEmpty(bucketName) || string.IsNullOrEmpty(objectName))
                {
                    return new BadRequestObjectResult("Invalid minio path format");
                }

                return action switch
                {
                    "url" => await GetFileUrl(bucketName, objectName),
                    "info" => await GetFileInfo(bucketName, objectName),
                    _ => await GetFileRedirect(bucketName, objectName)
                };
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "File not found or error accessing file {File}", path);
                return new NotFoundObjectResult("File not found");
            }
        }
        [HttpPost]
        [Route("UploadFile")]
        public async Task<BaseResponse<UploadResponse>> UploadFile(IFormFile file, string? path = null)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return GetResponse<UploadResponse>(ApiResult.Fail, null!, "No file provided");
                }
                var fileName = file.FileName;
                var objectName = $"{(!string.IsNullOrEmpty(path) ? path + "/" : string.Empty)}" + Guid.NewGuid() + "_" + fileName;
                // Use the original filename if objectName is not provided
                var finalObjectName = objectName ?? file.FileName;

                // Upload using stream from IFormFile
                using var stream = file.OpenReadStream();
                var result = await _storageService.UploadObjectAsync(
                    finalObjectName,
                    stream,
                    size: file.Length,
                    contentType: file.ContentType);

                // Convert StorageUploadResult to UploadResponse
                var uploadResponse = new UploadResponse
                {
                    Size = result.Size,
                    ObjectName = result.ObjectName,
                    Bucket = result.BucketName,
                    FilePath = result.FilePath,
                    FileName = file.FileName,
                    ContentType = result.ContentType ?? file.ContentType,
                    Url = await _storageService.GetPreviewUrlAsync(result.ObjectName, result.BucketName),
                    UrlExpiration = 0
                };

                return GetResponse(ApiResult.Success, uploadResponse);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error uploading file {FileName}", file?.FileName);
                return GetResponse<UploadResponse>(ApiResult.Fail, null!, e.Message);
            }
        }

        private async Task<IActionResult> GetFileUrl(string bucketName, string objectName)
        {
            var url = await _storageService.GetPreviewUrlAsync(objectName, bucketName);
            return Ok(url);
        }

        private async Task<IActionResult> GetFileInfo(string bucketName, string objectName)
        {
            // Get detailed file information
            var objectInfo = await _storageService.GetObjectInfoAsync(objectName, bucketName);
            var url = await _storageService.GetPreviewUrlAsync(objectName, bucketName);

            var fileInfo = new
            {
                ObjectName = objectInfo.ObjectName,
                Size = objectInfo.Size,
                LastModified = objectInfo.LastModified,
                ETag = objectInfo.ETag,
                ContentType = objectInfo.ContentType,
                StorageClass = objectInfo.StorageClass,
                IsDirectory = objectInfo.IsDirectory,
                Metadata = objectInfo.Metadata,
                Url = url,
                FilePath = objectInfo.FilePath,
                BucketName = bucketName
            };

            return Ok(GetResponse(ApiResult.Success, fileInfo));
        }

        private async Task<IActionResult> GetFileRedirect(string bucketName, string objectName)
        {
            var url = await _storageService.GetPreviewUrlAsync(objectName, bucketName);
            return Redirect(url);
        }

    }
}