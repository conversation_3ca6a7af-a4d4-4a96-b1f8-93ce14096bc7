using UNI.Common.CommonBase;
using UNI.Model;
using Uni.Personal.DAL.Interfaces;
using Uni.Personal.Model;

namespace Uni.Personal.DAL.Repositories
{
    /// <summary>
    /// Cart repository implementation
    /// </summary>
    public class CartRepository : UniBaseRepository, ICartRepository
    {
        public CartRepository(IUniCommonBaseRepository commonBase) : base(commonBase)
        {
        }

        public Task<CommonListPage> GetPageAsync(CartFilterInput? filter)
        {
            throw new NotImplementedException();
        }

        public Task<CommonViewOidInfo> GetInfoAsync(Guid? oid)
        {
            throw new NotImplementedException();
        }

        public Task<BaseValidate<Guid?>> SetInfoAsync(CommonViewOidInfo info)
        {
            throw new NotImplementedException();
        }

        public Task<BaseValidate<bool>> DeleteAsync(Guid? oid)
        {
            throw new NotImplementedException();
        }
    }
}
