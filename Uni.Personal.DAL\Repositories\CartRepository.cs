using UNI.Common.CommonBase;
using UNI.Model;
using Uni.Personal.DAL.Interfaces;
using Uni.Personal.Model;

namespace Uni.Personal.DAL.Repositories
{
    /// <summary>
    /// Cart repository implementation
    /// </summary>
    public class CartRepository : UniBaseRepository, ICartRepository
    {
        public CartRepository(IUniCommonBaseRepository commonBase) : base(commonBase)
        {
        }

        /// <summary>
        /// Get list of cart items
        /// </summary>
        /// <param name="filter">Filter criteria for cart items</param>
        /// <returns>List of cart items</returns>
        public async Task<IEnumerable<CartItem>> GetCartItemsAsync(CartFilterInput filter)
        {
            const string sp = "sp_personal_cart_list";
            return await GetAsync<CartItem>(sp, filter);
        }

        /// <summary>
        /// Add or update cart item
        /// </summary>
        /// <param name="cartItem">Cart item to add or update</param>
        /// <returns>Validation result with cart item ID</returns>
        public async Task<BaseValidate> SetCartItemAsync(CartItemInput cartItem)
        {
            const string sp = "sp_personal_cart_set";
            return await SetAsync(sp, cartItem);
        }

        /// <summary>
        /// Remove cart item
        /// </summary>
        /// <param name="cartItemId">Cart item ID to remove</param>
        /// <returns>Validation result</returns>
        public async Task<BaseValidate> RemoveCartItemAsync(Guid cartItemId)
        {
            const string sp = "sp_personal_cart_remove";
            return await DeleteAsync(sp, new { Id = cartItemId });
        }
    }
}
