using UNI.Common.CommonBase;
using UNI.Model;
using Uni.Personal.DAL.Interfaces;
using Uni.Personal.Model;

namespace Uni.Personal.DAL.Repositories
{
    /// <summary>
    /// Cart repository implementation
    /// </summary>
    public class CartRepository : UniBaseRepository, ICartRepository
    {
        public CartRepository(IUniCommonBaseRepository commonBase) : base(commonBase)
        {
        }
    }
}
