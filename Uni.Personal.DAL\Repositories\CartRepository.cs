using UNI.Common.CommonBase;
using UNI.Model;
using Uni.Personal.DAL.Interfaces;
using Uni.Personal.Model;

namespace Uni.Personal.DAL.Repositories
{
    /// <summary>
    /// Cart repository implementation
    /// </summary>
    public class CartRepository : UniBaseRepository, ICartRepository
    {
        public CartRepository(IUniCommonBaseRepository commonBase) : base(commonBase)
        {
        }

        /// <summary>
        /// Get paginated list of cart items
        /// </summary>
        /// <param name="filter">Filter input for cart items</param>
        /// <returns>Paginated list of cart items</returns>
        public async Task<CommonListPage> GetPageAsync(CartFilterInput? filter)
        {
            // Call stored procedure to get paginated cart items
            return await GetPageAsync("sp_personal_cart_page", filter);
        }

        /// <summary>
        /// Get cart information by ID
        /// </summary>
        /// <param name="oid">Cart ID</param>
        /// <returns>Cart information</returns>
        public async Task<CommonViewOidInfo> GetInfoAsync(Guid? oid)
        {
            // Call stored procedure to get cart info
            return await GetFieldsAsync<CommonViewOidInfo>("sp_personal_cart_fields", new { Oid = oid });
        }

        /// <summary>
        /// Create or update cart
        /// </summary>
        /// <param name="info">Cart information</param>
        /// <returns>Validation result with cart ID</returns>
        public async Task<BaseValidate<Guid?>> SetInfoAsync(CommonViewOidInfo info)
        {
            // Call stored procedure to create/update cart
            return await SetInfoAsync<BaseValidate<Guid?>>("sp_personal_cart_set", info, new { info.Oid });
        }

        /// <summary>
        /// Delete cart
        /// </summary>
        /// <param name="oid">Cart ID</param>
        /// <returns>Validation result</returns>
        public async Task<BaseValidate<bool>> DeleteAsync(Guid? oid)
        {
            // Call stored procedure to delete cart
            return await DeleteAsync<BaseValidate<bool>>("sp_personal_cart_del", new { Oid = oid });
        }
    }
}
