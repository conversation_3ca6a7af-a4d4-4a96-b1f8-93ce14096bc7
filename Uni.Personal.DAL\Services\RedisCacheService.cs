using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using System.Text.Json;
using Uni.Personal.DAL.Interfaces;

namespace Uni.Personal.DAL.Services
{
    /// <summary>
    /// Redis distributed cache service implementation
    /// </summary>
    public class RedisCacheService : ICacheService
    {
        private readonly IDistributedCache _distributedCache;
        private readonly IConnectionMultiplexer _connectionMultiplexer;
        private readonly ILogger<RedisCacheService> _logger;
        private readonly JsonSerializerOptions _jsonOptions;

        public RedisCacheService(
            IDistributedCache distributedCache,
            IConnectionMultiplexer connectionMultiplexer,
            ILogger<RedisCacheService> logger)
        {
            _distributedCache = distributedCache;
            _connectionMultiplexer = connectionMultiplexer;
            _logger = logger;
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = false
            };
        }

        /// <inheritdoc />
        public async Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default)
        {
            try
            {
                var cachedValue = await _distributedCache.GetStringAsync(key, cancellationToken);
                if (string.IsNullOrEmpty(cachedValue))
                {
                    return default;
                }

                return JsonSerializer.Deserialize<T>(cachedValue, _jsonOptions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cached value for key: {Key}", key);
                return default;
            }
        }

        /// <inheritdoc />
        public async Task SetAsync<T>(string key, T value, TimeSpan? expiration = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var serializedValue = JsonSerializer.Serialize(value, _jsonOptions);
                var options = new DistributedCacheEntryOptions();

                if (expiration.HasValue)
                {
                    options.SetAbsoluteExpiration(expiration.Value);
                }
                else
                {
                    // Default expiration of 1 hour if not specified
                    options.SetAbsoluteExpiration(TimeSpan.FromHours(1));
                }

                await _distributedCache.SetStringAsync(key, serializedValue, options, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting cached value for key: {Key}", key);
                throw;
            }
        }

        /// <inheritdoc />
        public async Task RemoveAsync(string key, CancellationToken cancellationToken = default)
        {
            try
            {
                await _distributedCache.RemoveAsync(key, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing cached value for key: {Key}", key);
                throw;
            }
        }

        /// <inheritdoc />
        public async Task RemoveAsync(IEnumerable<string> keys, CancellationToken cancellationToken = default)
        {
            try
            {
                var database = _connectionMultiplexer.GetDatabase();
                var keyArray = keys.Select(k => (RedisKey)k).ToArray();

                if (keyArray.Length > 0)
                {
                    await database.KeyDeleteAsync(keyArray);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing multiple cached values");
                throw;
            }
        }

        /// <inheritdoc />
        public async Task RemoveByPatternAsync(string pattern, CancellationToken cancellationToken = default)
        {
            try
            {
                var database = _connectionMultiplexer.GetDatabase();
                var server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());
                
                var keys = server.Keys(pattern: pattern);
                var keyArray = keys.ToArray();
                
                if (keyArray.Length > 0)
                {
                    await database.KeyDeleteAsync(keyArray);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing cached values by pattern: {Pattern}", pattern);
                throw;
            }
        }

        /// <inheritdoc />
        public async Task<bool> ExistsAsync(string key, CancellationToken cancellationToken = default)
        {
            try
            {
                var cachedValue = await _distributedCache.GetStringAsync(key, cancellationToken);
                return !string.IsNullOrEmpty(cachedValue);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if key exists: {Key}", key);
                return false;
            }
        }

        /// <inheritdoc />
        public async Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiration = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var cachedValue = await GetAsync<T>(key, cancellationToken);
                if (cachedValue != null)
                {
                    return cachedValue;
                }

                var newValue = await factory();
                await SetAsync(key, newValue, expiration, cancellationToken);
                return newValue;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetOrSetAsync for key: {Key}", key);
                throw;
            }
        }

        /// <inheritdoc />
        public async Task<Dictionary<string, T?>> GetManyAsync<T>(IEnumerable<string> keys, CancellationToken cancellationToken = default)
        {
            try
            {
                var result = new Dictionary<string, T?>();
                var keyList = keys.ToList();

                foreach (var key in keyList)
                {
                    var value = await GetAsync<T>(key, cancellationToken);
                    if (value != null)
                    {
                        result[key] = value;
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting multiple cached values");
                throw;
            }
        }

        /// <inheritdoc />
        public async Task SetManyAsync<T>(Dictionary<string, T> keyValuePairs, TimeSpan? expiration = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var tasks = keyValuePairs.Select(kvp => SetAsync(kvp.Key, kvp.Value, expiration, cancellationToken));
                await Task.WhenAll(tasks);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting multiple cached values");
                throw;
            }
        }

        /// <inheritdoc />
        public async Task RefreshAsync(string key, TimeSpan? expiration = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var database = _connectionMultiplexer.GetDatabase();
                var exists = await database.KeyExistsAsync(key);

                if (exists)
                {
                    var exp = expiration ?? TimeSpan.FromHours(1);
                    await database.KeyExpireAsync(key, exp);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing cache expiration for key: {Key}", key);
                throw;
            }
        }

        /// <inheritdoc />
        public async Task<IEnumerable<string>> GetKeysAsync(string pattern = "*", CancellationToken cancellationToken = default)
        {
            try
            {
                var server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());
                var keys = server.Keys(pattern: pattern);
                return keys.Select(k => k.ToString());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting keys by pattern: {Pattern}", pattern);
                throw;
            }
        }

        /// <inheritdoc />
        public async Task ClearAllAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                var server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());
                await server.FlushDatabaseAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing all cache");
                throw;
            }
        }

        /// <inheritdoc />
        public async Task<CacheStatistics?> GetStatisticsAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                var server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());
                var database = _connectionMultiplexer.GetDatabase();

                var stats = new CacheStatistics();

                // Get approximate key count using DBSIZE command
                var keyCount = await database.ExecuteAsync("DBSIZE");
                if (!keyCount.IsNull)
                {
                    stats.TotalKeys = (long)keyCount;
                }

                // Add basic connection info
                stats.AdditionalStats["ConnectionString"] = _connectionMultiplexer.Configuration;
                stats.AdditionalStats["IsConnected"] = _connectionMultiplexer.IsConnected;

                return stats;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cache statistics");
                return null;
            }
        }
    }
}
