using System;
using System.Threading.Tasks;
using UNI.Common.CommonBase;
using UNI.Model;
using Uni.Personal.Model;
using Uni.Personal.Model.AttachmentModels;

namespace Uni.Personal.DAL.Interfaces
{
    /// <summary>
    /// Attachment repository interface
    /// </summary>
    public interface IAttachmentRepository
    {
        Task<IEnumerable<AttachmentInfo>> GetAttachmentsAsync(Guid? groupField);
        Task<AttachmentSet?> SetAttachmentsAsync(AttachmentSet attachments);
    }
}
