using UNI.Model;
using Uni.Personal.Model;

namespace Uni.Personal.BLL.Interfaces
{
    /// <summary>
    /// Cart service interface
    /// </summary>
    public interface ICartService
    {
        /// <summary>
        /// Get paginated list of cart items
        /// </summary>
        /// <param name="query">Filter input for cart items</param>
        /// <returns>Paginated list of cart items</returns>
        Task<CommonListPage> GetPageAsync(CartFilterInput? query);

        /// <summary>
        /// Get cart information by ID
        /// </summary>
        /// <param name="oid">Cart ID</param>
        /// <returns>Cart information</returns>
        Task<CommonViewOidInfo> GetInfoAsync(Guid? oid);

        /// <summary>
        /// Create or update cart
        /// </summary>
        /// <param name="info">Cart information</param>
        /// <returns>Validation result with cart ID</returns>
        Task<BaseValidate<Guid?>> SetInfoAsync(CommonViewOidInfo info);

        /// <summary>
        /// Delete cart
        /// </summary>
        /// <param name="oid">Cart ID</param>
        /// <returns>Validation result</returns>
        Task<BaseValidate<bool>> DeleteAsync(Guid? oid);
    }
}
