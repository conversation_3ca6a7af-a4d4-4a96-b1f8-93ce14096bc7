namespace Uni.Personal.Model.Configuration
{
    /// <summary>
    /// Cache configuration settings
    /// </summary>
    public class CacheConfiguration
    {
        /// <summary>
        /// Redis connection string
        /// </summary>
        public string ConnectionString { get; set; } = string.Empty;

        /// <summary>
        /// Default cache expiration in minutes
        /// </summary>
        public int DefaultExpirationMinutes { get; set; } = 60;

        /// <summary>
        /// Cache key prefix
        /// </summary>
        public string KeyPrefix { get; set; } = "uni_personal:";

        /// <summary>
        /// Enable cache compression
        /// </summary>
        public bool EnableCompression { get; set; } = false;

        /// <summary>
        /// Redis database number
        /// </summary>
        public int Database { get; set; } = 0;

        /// <summary>
        /// Connection timeout in seconds
        /// </summary>
        public int ConnectTimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// Command timeout in seconds
        /// </summary>
        public int CommandTimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// Enable Redis connection retry
        /// </summary>
        public bool EnableRetry { get; set; } = true;

        /// <summary>
        /// Maximum retry attempts
        /// </summary>
        public int MaxRetryAttempts { get; set; } = 3;
    }
}
