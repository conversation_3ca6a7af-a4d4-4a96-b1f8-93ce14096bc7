using Microsoft.Extensions.DependencyInjection;
using NetCore.AutoRegisterDi;

namespace Uni.Personal.BLL
{
    public static class DependencyInjectionRegisterExtensions
    {
        public static IServiceCollection RegisterServices
            (this IServiceCollection services)
        {
            services.RegisterAssemblyPublicNonGenericClasses()
                .AsPublicImplementedInterfaces();
            return services;
        }
    }
}
