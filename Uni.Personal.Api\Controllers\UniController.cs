﻿using System.Security.Claims;
using System.Text;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using UNI.Model;
using UNI.Model.Api;

namespace Uni.Personal.Api.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    /// <author>taint</author>
    /// <createdDate>2/2/2016</createdDate>
    /// <seealso>
    ///     <cref>System.Web.Http.ApiController</cref>
    /// </seealso>
    public class UniController : ControllerBase
    {
        ///// <summary>
        ///// logger
        ///// </summary>
        protected readonly ILogger _logger;
        ///// <summary>
        ///// AppSettings
        ///// </summary>
        protected readonly AppSettings _appSettings;
        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="appSettings"></param>
        /// <param name="logger"></param>
        public UniController(IOptions<AppSettings> appSettings, ILoggerFactory logger)
        {
            _appSettings = appSettings.Value;
            _logger = logger.CreateLogger(GetType().Name);
        }

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="logger"></param>
        public UniController(ILoggerFactory logger)
        {
            _logger = logger.CreateLogger(GetType().Name);
        }

        /// <summary>
        /// Get Errors
        /// </summary>
        //protected string Errors
        //{
        //    get
        //    {
        //        try
        //        {
        //            var sb = new StringBuilder();

        //            foreach (var key in ModelState.Keys)
        //            {
        //                foreach (var error in ModelState[key]?.Errors)
        //                {
        //                    sb.Append("Key: " + key + " - " + "Error: " + error.ErrorMessage + " @ " + error.Exception + "<br/>");
        //                }
        //            }

        //            return sb.ToString();
        //        }
        //        catch (Exception e)
        //        {
        //            _logger.LogError(e.StackTrace);
        //            throw;
        //        }
        //    }
        //}
        /// <summary>
        /// UserId
        /// </summary>
        protected string? UserId
        {
            get
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId) && Request.Headers.TryGetValue("X-User-Id", out var headerUserId))
                {
                    userId = headerUserId.ToString();
                }

                return userId;
            }
        }
        /// <summary>
        /// UserName
        /// </summary>
        protected string? UserName => User.Claims.Where(c => c.Type == "name").Select(c1 => c1.Value).FirstOrDefault();
        /// <summary>
        /// ClientId
        /// </summary>
        protected string? ClientId => User.Claims.Where(c => c.Type == "client_id").Select(c1 => c1.Value).FirstOrDefault();
        /// <summary>
        /// Mã sản phâm
        /// </summary>
        protected string? ProductCode => User.Claims.Where(c => c.Type == "product_code").Select(c1 => c1.Value).FirstOrDefault();
        /// <summary>
        /// Control Client
        /// </summary>
        public BaseCtrlClient CtrlClient
        {
            get
            {
                return new BaseCtrlClient
                {
                    ClientId = User.Claims.Where(c => c.Type == "client_id").Select(c1 => c1.Value).FirstOrDefault(),
                    ClientIp = Request.HttpContext.Connection.RemoteIpAddress?.ToString(),
                    hostUrl = HttpContext.Request.Scheme + "://" + HttpContext.Request.Host.Value,
                    UserId = User.Claims.Where(c => c.Type == "sub").Select(c1 => c1.Value).FirstOrDefault()
                };
            }
        }
        /// <summary>
        /// GetClaim
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        protected string GetClaim(string key)
        {
            var principal = HttpContext.User.Claims;
            return principal.Single(c => c.Type == key).Value;
        }
        /// <summary>
        /// GetResponse
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="status"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        protected static BaseResponse<T> GetResponse<T>(ApiResult status, T data)
        {
            return new BaseResponse<T>(status, data);
        }
        /// <summary>
        /// Get Response
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="status"></param>
        /// <param name="data"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        protected static BaseResponse<T> GetResponse<T>(ApiResult status, T data, string message)
        {
            var response = new BaseResponse<T>(status, data);
            response.SetStatus(status, message);
            return response;
        }
        /// <summary>
        /// GetErrorResponse
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="result"></param>
        /// <param name="code"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        protected BaseResponse<T> GetErrorResponse<T>(ApiResult result, int code, string message)
        {
            // trả mã lỗi
            var response = new BaseResponse<T>();

            // thêm lỗi
            response.AddError(message);
            response.SetStatus(result, message);
            response.SetStatus(code, message);

            return response;
        }
        /// <summary>
        /// Accept-Language
        /// </summary>
        //protected string? AcceptLanguage => Request.Headers["Accept-Language"].ToString().Split(";").FirstOrDefault()?.Split(",").FirstOrDefault();
    }
}