using UNI.Model;
using Uni.Personal.BLL.Interfaces;
using Uni.Personal.DAL.Interfaces;
using Uni.Personal.Model;

namespace Uni.Personal.BLL.Services
{
    /// <summary>
    /// Cart service implementation
    /// </summary>
    public class CartService : ICartService
    {
        private readonly ICartRepository _cartRepository;

        public CartService(ICartRepository cartRepository)
        {
            _cartRepository = cartRepository;
        }

        public Task<CommonListPage> GetPageAsync(CartFilterInput? query)
        {
            throw new NotImplementedException();
        }

        public Task<CommonViewOidInfo> GetInfoAsync(Guid? oid)
        {
            throw new NotImplementedException();
        }

        public Task<BaseValidate<Guid?>> SetInfoAsync(CommonViewOidInfo info)
        {
            throw new NotImplementedException();
        }

        public Task<BaseValidate<bool>> DeleteAsync(Guid? oid)
        {
            throw new NotImplementedException();
        }
    }
}
