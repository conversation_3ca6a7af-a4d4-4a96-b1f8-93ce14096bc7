using UNI.Model;
using Uni.Personal.BLL.Interfaces;
using Uni.Personal.DAL.Interfaces;
using Uni.Personal.Model;

namespace Uni.Personal.BLL.Services
{
    /// <summary>
    /// Cart service implementation
    /// </summary>
    public class CartService : ICartService
    {
        private readonly ICartRepository _cartRepository;

        public CartService(ICartRepository cartRepository)
        {
            _cartRepository = cartRepository;
        }

        /// <summary>
        /// Get list of cart items
        /// </summary>
        /// <returns>List of cart items</returns>
        public async Task<IEnumerable<CartItem>> GetCartItemsAsync()
        {
            return await _cartRepository.GetCartItemsAsync();
        }

        /// <summary>
        /// Add or update cart item
        /// </summary>
        /// <param name="cartItem">Cart item to add or update</param>
        /// <returns>Validation result with cart item ID</returns>
        public async Task<BaseValidate> SetCartItemAsync(CartItemInput cartItem)
        {
            return await _cartRepository.SetCartItemAsync(cartItem);
        }

        /// <summary>
        /// Remove cart item
        /// </summary>
        /// <param name="cartItemId">Cart item ID to remove</param>
        /// <returns>Validation result</returns>
        public async Task<BaseValidate> RemoveCartItemAsync(Guid cartItemId)
        {
            return await _cartRepository.RemoveCartItemAsync(cartItemId);
        }
    }
}
