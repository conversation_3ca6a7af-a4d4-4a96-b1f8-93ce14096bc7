using UNI.Model;
using Uni.Personal.BLL.Interfaces;
using Uni.Personal.DAL.Interfaces;
using Uni.Personal.Model;

namespace Uni.Personal.BLL.Services
{
    /// <summary>
    /// Cart service implementation
    /// </summary>
    public class CartService : ICartService
    {
        private readonly ICartRepository _cartRepository;

        public CartService(ICartRepository cartRepository)
        {
            _cartRepository = cartRepository;
        }

        /// <summary>
        /// Get paginated list of cart items
        /// </summary>
        /// <param name="query">Filter input for cart items</param>
        /// <returns>Paginated list of cart items</returns>
        public async Task<CommonListPage> GetPageAsync(CartFilterInput? query)
        {
            return await _cartRepository.GetPageAsync(query);
        }

        /// <summary>
        /// Get cart information by ID
        /// </summary>
        /// <param name="oid">Cart ID</param>
        /// <returns>Cart information</returns>
        public async Task<CommonViewOidInfo> GetInfoAsync(Guid? oid)
        {
            return await _cartRepository.GetInfoAsync(oid);
        }

        /// <summary>
        /// Create or update cart
        /// </summary>
        /// <param name="info">Cart information</param>
        /// <returns>Validation result with cart ID</returns>
        public async Task<BaseValidate<Guid?>> SetInfoAsync(CommonViewOidInfo info)
        {
            return await _cartRepository.SetInfoAsync(info);
        }

        /// <summary>
        /// Delete cart
        /// </summary>
        /// <param name="oid">Cart ID</param>
        /// <returns>Validation result</returns>
        public async Task<BaseValidate<bool>> DeleteAsync(Guid? oid)
        {
            // Add business logic validation here if needed
            // For example: check if cart can be deleted, validate permissions, etc.

            return await _cartRepository.DeleteAsync(oid);
        }
    }
}
