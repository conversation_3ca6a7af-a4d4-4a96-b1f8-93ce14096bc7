using UNI.Model;

namespace Uni.Personal.Model
{
    /// <summary>
    /// Attachment information model
    /// </summary>
    public class AttachmentInfo
    {
        public Guid? Id { get; set; }
        public string? FileName { get; set; }
        public string? contentType { get; set; }
        public string? FileExtension { get; set; }
        public string? FilePath { get; set; }
        public string? StoragePath { get; set; }
        public long? Size { get; set; }
        public string? ContentType { get; set; }
        public string? Hash { get; set; }
        public string? Description { get; set; }
    }
}
