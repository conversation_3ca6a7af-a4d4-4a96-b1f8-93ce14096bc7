﻿using System.Collections.Generic;
using System.Threading.Tasks;
using UNI.Model;
using UNI.Model.APPM;
using UNI.Model.Core;

namespace Uni.Personal.DAL.Interfaces
{

    public interface IAppUserRepository
    {
        Task<coreUserLoginResponse> SetUserRegister(coreUserLoginReg reg);
        Task<coreUserLoginResponse> GetUserRegisted(string reg_id);
        Task<userLoginRefesh> SetVerificated(BaseCtrlClient clt, coreVerify code, string userId);
        Task LockUser(string userId, bool isLock);
        Task<BaseValidate> SetUserProfile(string userId, UserProfileSet ureg);
        Task<userProfileSet> GetUserProfile(string userLogin);
        Task<userForegetResponse> GetUserForgetPassword(BaseCtrlClient clt, string loginName, string udid);
        Task<coreUserLoginResponse> SetUserForgetPassword(BaseCtrlClient clt, userForegetSet forget);
        Task<coreUserLoginResponse> SetUserForgetVerificated(coreVerify code, int user_type);
        
        
        Task<coreUserLoginResponse> SetResendCode(string reg_id);
        Task<UserProfileFull> GetProfileFull(string loginName);
        Task<List<viewField>> GetProfileFields(string userId, string loginName);
        Task SetProfileFields(string userId, viewField fields);

        #region device
        Task<BaseValidate> SetSmartDevice(BaseCtrlClient clt, userSmartDevice device);
        Task<BaseValidate> DeleteSmartDevice(BaseCtrlClient clt, string udid);
        Task<userSmartDevicePage> GetSmartDevices(FilterBase flt);
        Task<userOtpResponse> SetSmartDeviceConfirm(BaseCtrlClient clt, userSmartDeviceConfirm confirm);
        Task<userOtpResponse> GetSmartDeviceVerify(BaseCtrlClient clt, userSmartDeviceVerify verify);
        Task<BaseValidate> SetSmartDeviceVerificated(BaseCtrlClient clt, userSmartDeviceVerify verify, int status);
        #endregion
    }
}
