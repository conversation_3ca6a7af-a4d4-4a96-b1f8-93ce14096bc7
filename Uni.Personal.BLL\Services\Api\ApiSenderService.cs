﻿using Uni.Personal.BLL.BusinessInterfaces.Api;
using Uni.Personal.DAL.Interfaces.Api;
using UNI.Model.APPM;

namespace Uni.Personal.BLL.BusinessService.Api
{
    public class ApiSenderService : IApiSenderService
    {
        private readonly IApiSenderRepository _apisenderRepository;
        
        public ApiSenderService(IApiSenderRepository hrmRepository
            )
        {
                _apisenderRepository = hrmRepository;
        }
        public Task<MessageRespone> SendSmsAs(MessageBase send)
        {
            return _apisenderRepository.SendSmsAs(send);
        }
        public Task SendMailgunEmail(EmailBase emailModel)
        {
            return _apisenderRepository.SendMailgunEmail(emailModel);
        }
        
    }
}
