using System;
using System.Threading.Tasks;
using UNI.Common.CommonBase;
using UNI.Model;
using Uni.Personal.DAL.Interfaces;
using Uni.Personal.Model;
using Uni.Personal.Model.AttachmentModels;
using Dapper;
using DapperParameters;

namespace Uni.Personal.DAL.Repositories
{
    /// <summary>
    /// Attachment repository implementation
    /// </summary>
    public class AttachmentRepository : UniBaseRepository, IAttachmentRepository
    {
        public AttachmentRepository(IUniCommonBaseRepository commonBase) : base(commonBase)
        {
        }

        public Task<IEnumerable<AttachmentInfo>> GetAttachmentsAsync(Guid? groupField)
        {
            const string sp = "sp_Attachment_get";
            return base.GetAsync<AttachmentInfo>(sp, new { groupField });
        }

        public async Task<AttachmentSet?> SetAttachmentsAsync(AttachmentSet attachment)
        {
            const string sp = "sp_Attachment_set";
            var param = new DynamicParameters();
            param.Add("@GroupField", attachment.GroupField);
            if (attachment.Attachments != null)
            {
                param.AddTable("@Attachments", "AttachmentType", attachment.Attachments);
            }
            var rs = await GetFirstOrDefaultAsync<BaseValidate>(sp, param);
            if (!rs.valid) return null;
            attachment.GroupField = rs.id;
            return attachment;
        }

    }
}
