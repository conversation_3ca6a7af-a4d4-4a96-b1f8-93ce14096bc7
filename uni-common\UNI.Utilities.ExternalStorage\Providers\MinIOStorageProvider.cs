using Microsoft.Extensions.Logging;
using UNI.Utilities.ExternalStorage.Abstractions;
using UNI.Utilities.ExternalStorage.Exceptions;
using UNI.Utilities.ExternalStorage.Models;
using UNI.Utilities.MinIo;

namespace UNI.Utilities.ExternalStorage.Providers
{
    /// <summary>
    /// MinIO storage provider implementation
    /// </summary>
    public class MinIOStorageProvider : BaseStorageProvider<MinIOStorageSettings>
    {
        private readonly IMinIoService _minIoService;

        /// <summary>
        /// Constructor for MinIOStorageProvider
        /// </summary>
        /// <param name="settings">MinIO storage settings</param>
        /// <param name="logger">Logger instance</param>
        public MinIOStorageProvider(MinIOStorageSettings settings, ILogger<MinIOStorageProvider> logger)
            : base(settings, logger)
        {
            // Create MinIO service with converted settings
            var minIoSettings = ConvertToMinIoSettings(settings);

            // Create a logger for MinIoService using a factory
            var loggerFactory = new LoggerFactory();
            var minIoLogger = loggerFactory.CreateLogger<MinIoService>();

            _minIoService = new MinIoService(minIoSettings, minIoLogger);
        }

        /// <summary>
        /// Storage provider type
        /// </summary>
        public override StorageProviderType ProviderType => StorageProviderType.MinIO;

        /// <summary>
        /// Storage provider name
        /// </summary>
        public override string ProviderName => "MinIO";

        /// <summary>
        /// Validate MinIO-specific settings
        /// </summary>
        protected override void ValidateSettings()
        {
            base.ValidateSettings();

            if (string.IsNullOrWhiteSpace(_settings.Endpoint))
                throw new StorageConfigurationException("MinIO Endpoint is required", ProviderType);

            if (string.IsNullOrWhiteSpace(_settings.AccessKey))
                throw new StorageConfigurationException("MinIO AccessKey is required", ProviderType);

            if (string.IsNullOrWhiteSpace(_settings.SecretKey))
                throw new StorageConfigurationException("MinIO SecretKey is required", ProviderType);
        }

        /// <summary>
        /// Convert external storage settings to MinIO settings
        /// </summary>
        /// <param name="settings">External storage settings</param>
        /// <returns>MinIO settings</returns>
        private static MinIoSettings ConvertToMinIoSettings(MinIOStorageSettings settings)
        {
            return new MinIoSettings
            {
                Endpoint = settings.Endpoint,
                ProxyEndpoint = settings.ProxyEndpoint,
                AccessKey = settings.AccessKey,
                SecretKey = settings.SecretKey,
                UseSSL = settings.UseSSL,
                Region = settings.Region,
                DefaultBucket = settings.DefaultBucket,
                TimeoutSeconds = settings.TimeoutSeconds,
                CreateBucketIfNotExists = settings.CreateBucketIfNotExists
            };
        }

        #region Bucket Operations

        public override async Task<bool> BucketExistsAsync(string? bucketName = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var resolvedBucketName = GetBucketName(bucketName);
                return await _minIoService.BucketExistsAsync(resolvedBucketName, cancellationToken);
            }
            catch (Exception ex)
            {
                var resolvedBucketName = GetBucketName(bucketName);
                throw new StorageException($"Failed to check if bucket '{resolvedBucketName}' exists", ProviderType, innerException: ex);
            }
        }

        public override async Task CreateBucketAsync(string? bucketName = null, string? region = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var resolvedBucketName = GetBucketName(bucketName);
                await _minIoService.CreateBucketAsync(resolvedBucketName, region, cancellationToken);
            }
            catch (Exception ex)
            {
                var resolvedBucketName = GetBucketName(bucketName);
                throw new StorageException($"Failed to create bucket '{resolvedBucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task DeleteBucketAsync(string? bucketName = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var resolvedBucketName = GetBucketName(bucketName);
                await _minIoService.DeleteBucketAsync(resolvedBucketName, cancellationToken);
            }
            catch (Exception ex)
            {
                var resolvedBucketName = GetBucketName(bucketName);
                throw new StorageException($"Failed to delete bucket '{resolvedBucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<IEnumerable<Models.BucketInfo>> ListBucketsAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                var buckets = await _minIoService.ListBucketsAsync(cancellationToken);
                return buckets.Select(ConvertToBucketInfo);
            }
            catch (Exception ex)
            {
                throw new StorageException("Failed to list buckets", ProviderType, innerException: ex);
            }
        }

        #endregion

        #region Object Operations

        public override async Task<StorageUploadResult> UploadObjectAsync(
            string objectName, 
            Stream stream, 
            string? bucketName = null,
            long? size = null, 
            string? contentType = null, 
            Dictionary<string, string>? metadata = null, 
            CancellationToken cancellationToken = default)
        {
            try
            {
                var resolvedBucketName = GetBucketName(bucketName);
                await EnsureBucketExistsAsync(resolvedBucketName, cancellationToken);
                var result = await _minIoService.UploadObjectAsync(resolvedBucketName, objectName, stream, size, contentType, metadata, cancellationToken);
                return ConvertToStorageUploadResult(result);
            }
            catch (Exception ex)
            {
                var resolvedBucketName = GetBucketName(bucketName);
                throw new StorageException($"Failed to upload object '{objectName}' to bucket '{resolvedBucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<StorageUploadResult> UploadFileAsync(
            string objectName, 
            string filePath, 
            string? bucketName = null,
            string? contentType = null, 
            Dictionary<string, string>? metadata = null, 
            CancellationToken cancellationToken = default)
        {
            try
            {
                var resolvedBucketName = GetBucketName(bucketName);
                await EnsureBucketExistsAsync(resolvedBucketName, cancellationToken);
                var result = await _minIoService.UploadFileAsync(resolvedBucketName, objectName, filePath, contentType, metadata, cancellationToken);
                return ConvertToStorageUploadResult(result);
            }
            catch (Exception ex)
            {
                var resolvedBucketName = GetBucketName(bucketName);
                throw new StorageException($"Failed to upload file '{filePath}' as object '{objectName}' to bucket '{resolvedBucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<StorageUploadResult> UploadLargeFileAsync(
            string objectName, 
            string filePath, 
            string? bucketName = null,
            string? contentType = null, 
            Dictionary<string, string>? metadata = null, 
            long partSize = 67108864, 
            IProgress<StorageUploadProgress>? progressCallback = null, 
            CancellationToken cancellationToken = default)
        {
            try
            {
                var resolvedBucketName = GetBucketName(bucketName);
                await EnsureBucketExistsAsync(resolvedBucketName, cancellationToken);
                
                // Convert progress callback
                IProgress<UploadProgress>? minIoProgressCallback = null;
                if (progressCallback != null)
                {
                    minIoProgressCallback = new Progress<UploadProgress>(progress =>
                    {
                        var storageProgress = ConvertToStorageUploadProgress(progress);
                        progressCallback.Report(storageProgress);
                    });
                }

                var result = await _minIoService.UploadLargeFileAsync(resolvedBucketName, objectName, filePath, contentType, metadata, partSize, minIoProgressCallback, cancellationToken);
                return ConvertToStorageUploadResult(result);
            }
            catch (Exception ex)
            {
                var resolvedBucketName = GetBucketName(bucketName);
                throw new StorageException($"Failed to upload large file '{filePath}' as object '{objectName}' to bucket '{resolvedBucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task DownloadObjectAsync(string objectName, Stream stream, string? bucketName = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var resolvedBucketName = GetBucketName(bucketName);
                await _minIoService.DownloadObjectAsync(resolvedBucketName, objectName, stream, cancellationToken);
            }
            catch (Exception ex)
            {
                var resolvedBucketName = GetBucketName(bucketName);
                throw new StorageException($"Failed to download object '{objectName}' from bucket '{resolvedBucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task DownloadFileAsync(string objectName, string filePath, string? bucketName = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var resolvedBucketName = GetBucketName(bucketName);
                await _minIoService.DownloadFileAsync(resolvedBucketName, objectName, filePath, cancellationToken);
            }
            catch (Exception ex)
            {
                var resolvedBucketName = GetBucketName(bucketName);
                throw new StorageException($"Failed to download object '{objectName}' from bucket '{resolvedBucketName}' to file '{filePath}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<byte[]> GetObjectBytesAsync(string objectName, string? bucketName = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var resolvedBucketName = GetBucketName(bucketName);
                return await _minIoService.GetObjectBytesAsync(resolvedBucketName, objectName, cancellationToken);
            }
            catch (Exception ex)
            {
                var resolvedBucketName = GetBucketName(bucketName);
                throw new StorageException($"Failed to get object bytes for '{objectName}' from bucket '{resolvedBucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<bool> ObjectExistsAsync(string objectName, string? bucketName = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var resolvedBucketName = GetBucketName(bucketName);
                return await _minIoService.ObjectExistsAsync(resolvedBucketName, objectName, cancellationToken);
            }
            catch (Exception ex)
            {
                var resolvedBucketName = GetBucketName(bucketName);
                throw new StorageException($"Failed to check if object '{resolvedBucketName}' exists in bucket '{objectName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task DeleteObjectAsync(string objectName, string? bucketName = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var resolvedBucketName = GetBucketName(bucketName);
                await _minIoService.DeleteObjectAsync(resolvedBucketName, objectName, cancellationToken);
            }
            catch (Exception ex)
            {
                var resolvedBucketName = GetBucketName(bucketName);
                throw new StorageException($"Failed to delete object '{objectName}' from bucket '{resolvedBucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<IEnumerable<StorageDeleteResult>> DeleteObjectsAsync(IEnumerable<string> objectNames, string? bucketName = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var resolvedBucketName = GetBucketName(bucketName);
                var results = await _minIoService.DeleteObjectsAsync(resolvedBucketName, objectNames, cancellationToken);
                return results.Select(ConvertToStorageDeleteResult);
            }
            catch (Exception ex)
            {
                var resolvedBucketName = GetBucketName(bucketName);
                throw new StorageException($"Failed to delete objects from bucket '{resolvedBucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<StorageObjectInfo> GetObjectInfoAsync(string objectName, string? bucketName = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var resolvedBucketName = GetBucketName(bucketName);
                var objectInfo = await _minIoService.GetObjectInfoAsync(resolvedBucketName, objectName, cancellationToken);
                return ConvertToStorageObjectInfo(objectInfo);
            }
            catch (Exception ex)
            {
                var resolvedBucketName = GetBucketName(bucketName);
                throw new StorageException($"Failed to get object info for '{objectName}' from bucket '{resolvedBucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<IEnumerable<StorageObjectInfo>> ListObjectsAsync(string? prefix = null, bool recursive = true, string? bucketName = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var resolvedBucketName = GetBucketName(bucketName);
                var objects = await _minIoService.ListObjectsAsync(resolvedBucketName, prefix, recursive, cancellationToken);
                return objects.Select(ConvertToStorageObjectInfo);
            }
            catch (Exception ex)
            {
                var resolvedBucketName = GetBucketName(bucketName);
                throw new StorageException($"Failed to list objects in bucket '{resolvedBucketName}'", ProviderType, innerException: ex);
            }
        }

        #endregion

        #region URL Operations

        public override async Task<string> GetPresignedDownloadUrlAsync(string objectName, TimeSpan expiryTimeSpan, string? bucketName = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var resolvedBucketName = GetBucketName(bucketName);
                return await _minIoService.GetPresignedDownloadUrlAsync(resolvedBucketName, objectName, expiryTimeSpan, cancellationToken);
            }
            catch (Exception ex)
            {
                var resolvedBucketName = GetBucketName(bucketName);
                throw new StorageException($"Failed to get presigned download URL for object '{objectName}' from bucket '{resolvedBucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<string> GetPresignedUploadUrlAsync(string objectName, TimeSpan expiryTimeSpan, string? bucketName = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var resolvedBucketName = GetBucketName(bucketName);
                return await _minIoService.GetPresignedUploadUrlAsync(resolvedBucketName, objectName, expiryTimeSpan, cancellationToken);
            }
            catch (Exception ex)
            {
                var resolvedBucketName = GetBucketName(bucketName);
                throw new StorageException($"Failed to get presigned upload URL for object '{objectName}' to bucket '{resolvedBucketName}'", ProviderType, innerException: ex);
            }
        }

        public override async Task<string> GetPreviewUrlAsync(string objectName, string? bucketName = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var resolvedBucketName = GetBucketName(bucketName);
                return await _minIoService.GetPreviewUrlAsync(resolvedBucketName, objectName, null, cancellationToken);
            }
            catch (Exception ex)
            {
                var resolvedBucketName = GetBucketName(bucketName);
                throw new StorageException($"Failed to get preview URL for object '{objectName}' from bucket '{resolvedBucketName}'", ProviderType, innerException: ex);
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Convert MinIO bucket info to storage bucket info
        /// </summary>
        /// <param name="bucketInfo">MinIO bucket info</param>
        /// <returns>Storage bucket info</returns>
        private static Models.BucketInfo ConvertToBucketInfo(MinIo.BucketInfo bucketInfo)
        {
            return new Models.BucketInfo
            {
                Name = bucketInfo.Name,
                CreationDate = bucketInfo.CreationDate
            };
        }

        /// <summary>
        /// Convert MinIO upload result to storage upload result
        /// </summary>
        /// <param name="result">MinIO upload result</param>
        /// <returns>Storage upload result</returns>
        private static StorageUploadResult ConvertToStorageUploadResult(UploadResult result)
        {
            return new StorageUploadResult
            {
                BucketName = result.BucketName,
                ObjectName = result.ObjectName,
                Size = result.Size,
                ETag = result.ETag,
                ContentType = result.ContentType,
                Metadata = new(), // MinIO UploadResult doesn't have Metadata property
                FilePath = $"minio://{result.BucketName}/{result.ObjectName}",
                UploadedAt = result.UploadTime // MinIO uses UploadTime instead of UploadedAt
            };
        }

        /// <summary>
        /// Convert MinIO upload progress to storage upload progress
        /// </summary>
        /// <param name="progress">MinIO upload progress</param>
        /// <returns>Storage upload progress</returns>
        private static StorageUploadProgress ConvertToStorageUploadProgress(UploadProgress progress)
        {
            return new StorageUploadProgress
            {
                UploadedBytes = progress.UploadedBytes, // StorageUploadProgress uses UploadedBytes
                TotalBytes = progress.TotalBytes,
                CurrentPart = progress.CurrentPart,
                TotalParts = progress.TotalParts,
                EstimatedTimeRemaining = progress.EstimatedTimeRemaining,
                BytesPerSecond = progress.BytesPerSecond
            };
        }

        /// <summary>
        /// Convert MinIO delete result to storage delete result
        /// </summary>
        /// <param name="result">MinIO delete result</param>
        /// <returns>Storage delete result</returns>
        private static StorageDeleteResult ConvertToStorageDeleteResult(DeleteResult result)
        {
            return new StorageDeleteResult
            {
                ObjectName = result.ObjectName,
                Success = result.IsSuccess, // MinIO uses IsSuccess instead of Success
                ErrorMessage = result.ErrorMessage
            };
        }

        /// <summary>
        /// Convert MinIO object info to storage object info
        /// </summary>
        /// <param name="objectInfo">MinIO object info</param>
        /// <returns>Storage object info</returns>
        private static StorageObjectInfo ConvertToStorageObjectInfo(ObjectInfo objectInfo)
        {
            return new StorageObjectInfo
            {
                ObjectName = objectInfo.ObjectName,
                Size = objectInfo.Size,
                LastModified = objectInfo.LastModified,
                ETag = objectInfo.ETag,
                ContentType = objectInfo.ContentType,
                StorageClass = objectInfo.StorageClass,
                IsDirectory = objectInfo.IsDir,
                Metadata = objectInfo.Metadata,
                ProviderData = new Dictionary<string, object>
                {
                    ["MinIOBucketName"] = objectInfo.BucketName
                },
                FilePath = $"minio://{objectInfo.BucketName}/{objectInfo.ObjectName}"
            };
        }

        /// <summary>
        /// Dispose the MinIO service
        /// </summary>
        protected override void DisposeCore()
        {
            _minIoService?.Dispose();
        }

        #endregion
    }
}
