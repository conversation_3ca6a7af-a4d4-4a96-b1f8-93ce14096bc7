using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using UNI.Model;
using UNI.Model.Api;
using Uni.Personal.BLL.Interfaces;
using Uni.Personal.Model;

namespace Uni.Personal.Api.Controllers.Version1
{
    [Route("/api/v1/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class ContractController : UniController
    {
        private readonly IContractService _contractService;

        public ContractController(IContractService contractService, ILoggerFactory logger) : base(logger)
        {
            _contractService = contractService;
        }

        /// <summary>
        /// Get paginated list of contracts
        /// </summary>
        /// <param name="query">Filter criteria for contracts</param>
        /// <returns>Paginated list of contracts</returns>
        [HttpGet]
        public async Task<BaseResponse<CommonListPage>> GetPage([FromQuery] ContractFilterInput? query)
        {
            if (query == null)
            {
                return GetErrorResponse<CommonListPage>(ApiResult.Error, 12, "Invalid request data");
            }
            var result = await _contractService.GetPageAsync(query);
            return GetResponse(ApiResult.Success, result);
        }
    }
}
