﻿using System;
using System.Collections.Generic;
using System.Text;

namespace UNI.Model.Core
{
    public class coreUserIdcardNo
    {
        public string idcard_no { get; set; }
    }
    public class coreUserIdcardInfo: coreUserIdcardList
    {
        public List<coreUserIdcardList> idcards { get; set; }
    }

    public class coreUserIdcardList
    {
        public string idcard_name { get; set; }
        public string idcard_no { get; set; }
        public bool idcard_verified { get; set; }
        public double recognition_rt { get; set; }
    }
    public class coreUserIdcard
    {
        public string idcard_name { get; set; }
        public int idcard_type { get; set; }
        public string idcard_no { get; set; }
        public string fullName { get; set; }
        public bool? sex { get; set; }
        public string birthday { get; set; }
        public string idcard_issue_dt { get; set; }
        public string idcard_expire_dt { get; set; }
        public string idcard_issue_plc { get; set; }
        public string origin_add { get; set; }
        public string res_add { get; set; }
        public string res_cntry { get; set; }
        public double recognition_rt { get; set; }
        public bool idcard_verified { get; set; }
        public List<coreUserProfileMeta> metas { get; set; }
    }
    public class AddressParam
    {
        public Guid? province { get; set; }
        public Guid? district { get; set; }
        public Guid? commune { get; set; }
        public string street { get; set; }
    }
}
