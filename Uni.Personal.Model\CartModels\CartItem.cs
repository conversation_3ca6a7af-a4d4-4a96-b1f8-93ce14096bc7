namespace Uni.Personal.Model
{
    /// <summary>
    /// Cart item model
    /// </summary>
    public class CartItem
    {
        public Guid Id { get; set; }
        public Guid? UserId { get; set; }
        public Guid? ProductId { get; set; }
        public Guid? PackageId { get; set; }
        public int Quantity { get; set; }
        public int? Ordinal { get; set; }
        public DateTime? Created { get; set; }
    }

    /// <summary>
    /// Cart item input for set operations
    /// </summary>
    public class CartItemInput
    {
        public Guid? Id { get; set; }
        public Guid? ProductId { get; set; }
        public Guid? PackageId { get; set; }
        public int Quantity { get; set; }
        public int? Ordinal { get; set; }
    }
}
