<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
	  <PackageReference Include="Confluent.Kafka" Version="2.5.1" />
	  <PackageReference Include="FluentEmail.Mailgun" Version="2.3.1" />
	  <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.7" />
	  <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.7" />
      <PackageReference Include="NetCore.AutoRegisterDi" Version="2.2.1" />
	  <PackageReference Include="Keycloak.Net.Core.v19" Version="1.0.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\uni-common\UNI.Utilities.HttpClientExtension\UNI.Utilities.HttpClientExtension.csproj" />
    <ProjectReference Include="..\Uni.Personal.Model\Uni.Personal.Model.csproj" />
    <ProjectReference Include="..\uni-common\UNI.Common\UNI.Common.csproj" />
    <ProjectReference Include="..\uni-common\UNI.Model\UNI.Model.csproj" />
  </ItemGroup>

</Project>
