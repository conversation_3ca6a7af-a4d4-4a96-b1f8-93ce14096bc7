﻿<Project Sdk="Microsoft.NET.Sdk"> 

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AssemblyName>UNI.Model</AssemblyName>
    <PackageId>UNI.Model</PackageId>    
    <GenerateAssemblyConfigurationAttribute>false</GenerateAssemblyConfigurationAttribute>
    <GenerateAssemblyCompanyAttribute>false</GenerateAssemblyCompanyAttribute>
    <GenerateAssemblyProductAttribute>false</GenerateAssemblyProductAttribute>
    <Configurations>Release;Debug</Configurations>
    <LangVersion>latest</LangVersion>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'" />

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'" />

  <ItemGroup>
    <Compile Remove="Audit\Bank\**" />
    <Compile Remove="Bond\**" />
    <Compile Remove="Construction\**" />
    <Compile Remove="HRM\**" />
    <Compile Remove="Investment\**" />
    <Compile Remove="KCod\**" />
    <Compile Remove="KInvest\**" />
    <Compile Remove="KRent\**" />
    <Compile Remove="KResidence\**" />
    <Compile Remove="KSHome\**" />
    <Compile Remove="Kss\**" />
    <Compile Remove="SupApp\**" />
    <Compile Remove="UniMaster\**" />
    <Compile Remove="UNI_Asset\**" />
    <Compile Remove="YamahaTraining\**" />
    <EmbeddedResource Remove="Audit\Bank\**" />
    <EmbeddedResource Remove="Bond\**" />
    <EmbeddedResource Remove="Construction\**" />
    <EmbeddedResource Remove="HRM\**" />
    <EmbeddedResource Remove="Investment\**" />
    <EmbeddedResource Remove="KCod\**" />
    <EmbeddedResource Remove="KInvest\**" />
    <EmbeddedResource Remove="KRent\**" />
    <EmbeddedResource Remove="KResidence\**" />
    <EmbeddedResource Remove="KSHome\**" />
    <EmbeddedResource Remove="Kss\**" />
    <EmbeddedResource Remove="SupApp\**" />
    <EmbeddedResource Remove="UniMaster\**" />
    <EmbeddedResource Remove="UNI_Asset\**" />
    <EmbeddedResource Remove="YamahaTraining\**" />
    <None Remove="Audit\Bank\**" />
    <None Remove="Bond\**" />
    <None Remove="Construction\**" />
    <None Remove="HRM\**" />
    <None Remove="Investment\**" />
    <None Remove="KCod\**" />
    <None Remove="KInvest\**" />
    <None Remove="KRent\**" />
    <None Remove="KResidence\**" />
    <None Remove="KSHome\**" />
    <None Remove="Kss\**" />
    <None Remove="SupApp\**" />
    <None Remove="UniMaster\**" />
    <None Remove="UNI_Asset\**" />
    <None Remove="YamahaTraining\**" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Account\OrderInfoModel.cs" />
    <Compile Remove="Account\OrderToChangeAcc.cs" />
    <Compile Remove="Api\Item.cs" />
    <Compile Remove="Api\NotificationMethod.cs" />
    <Compile Remove="APPM\ChangeBankAccNotify.cs" />
    <Compile Remove="APPM\cltApp.cs" />
    <Compile Remove="APPM\cltUserRole.cs" />
    <Compile Remove="APPM\cltWeb.cs" />
    <Compile Remove="APPM\cltWebAction.cs" />
    <Compile Remove="APPM\cltWebMenu.cs" />
    <Compile Remove="APPM\cltWebRole.cs" />
    <Compile Remove="APPM\cltWebTab.cs" />
    <Compile Remove="APPM\Contact.cs" />
    <Compile Remove="APPM\Notifications\AppNotifyPage.cs" />
    <Compile Remove="APPM\Notifications\NotificationVisitActions.cs" />
    <Compile Remove="Countable.cs" />
    <Compile Remove="EAction.cs" />
    <Compile Remove="Firestore\hrmFbEmpTerminate.cs" />
    <Compile Remove="Firestore\fbHrmLeaveRequest.cs" />
    <Compile Remove="Inbox.cs" />
    <Compile Remove="PagingParams.cs" />
    <Compile Remove="PagingParamsCustomer.cs" />
    <Compile Remove="SHousing\SreDevice.cs" />
    <Compile Remove="SHousing\SreIntroType.cs" />
    <Compile Remove="SHousing\SreProjectIntro.cs" />
    <Compile Remove="SHousing\SreRoomOrderV2.cs" />
    <Compile Remove="SmsGateway.cs" />
    <Compile Remove="SMS\SmsCustomer.cs" />
    <Compile Remove="SMS\SmsCustomerCategory.cs" />
    <Compile Remove="Template.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Dapper" Version="2.0.35" />
    <PackageReference Include="Dapper.ParameterExtensions" Version="2018.12.7.1" />
    <PackageReference Include="ExpressiveAnnotationsCore.dll" Version="0.1.0" />
    <PackageReference Include="Google.Cloud.Firestore" Version="3.8.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="NJsonSchema" Version="10.1.23" />
    <PackageReference Include="NSwag.Annotations" Version="13.10.1" />
    <PackageReference Include="System.ComponentModel.Annotations" Version="4.7.0" />
    <PackageReference Include="System.ComponentModel.Primitives" Version="4.3.0" />
    <!--<PackageReference Include="System.Data.SqlClient" Version="4.8.5" />-->
    <!--<PackageReference Include="System.Reflection.TypeExtensions" Version="4.4.0" />-->
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\UNI.Utils\UNI.Utils.csproj" />
  </ItemGroup>

</Project>
