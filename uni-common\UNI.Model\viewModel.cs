﻿using Dapper;
using Newtonsoft.Json;
using NSwag.Annotations;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Dynamic;
using System.Linq;
using System.Reflection;

namespace UNI.Model
{
    public class viewBaseInfo
    {
        [OpenApiIgnore]
        [JsonIgnore]
        public List<string> ErrorMessages { get; set; } = new List<string>();
        public List<viewGroup> group_fields { get; set; }
        public string tableKey { get; set; }
        public string groupKey { get; set; }
        public string draftPath { get; set; }
        public string submitPath { get; set; }
        public string fieldName { get; set; }
        public WorkStatus status { get; set; }
        public string GetValueByFieldName(string fieldName)
        {
            var fields = this.group_fields.SelectMany(x => x.fields);
            var f = fields.FirstOrDefault(x => x.field_name.Equals(fieldName, StringComparison.InvariantCultureIgnoreCase));
            if (f == null) return null;
            return string.IsNullOrEmpty(f.columnValue?.Trim()) ? null : f.columnValue;
        }
        public void SetValueByFieldName(string fieldName, string fieldValue)
        {
            var fields = this.group_fields.SelectMany(x => x.fields);
            var f = fields.FirstOrDefault(x => x.field_name.Equals(fieldName, StringComparison.InvariantCultureIgnoreCase));
            if (f != null)
            {
                f.columnValue = fieldValue;
            }
        }
        public DynamicParameters ConvertToParam()
        {
            var param = new DynamicParameters();
            var fields = this.group_fields.SelectMany(x => x.fields);
            foreach (var viewField in fields)
            {
                if (!viewField.isDisable)
                {
                    param.Add(viewField.field_name, string.IsNullOrEmpty(viewField.columnValue?.Trim()) ? null : viewField.columnValue);
                }

            }
            return param;
        }
        public DateTime? GetDatetimeValueByFieldName(string fieldName)
        {
            var fields = this.group_fields.SelectMany(x => x.fields);
            var f = fields.FirstOrDefault(x => x.field_name.Equals(fieldName));
            if (f == null) return null;
            if (string.IsNullOrEmpty(f.columnValue?.Trim())) return null;
            return DateTime.ParseExact(f.columnValue, "dd/MM/yyyy HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture);
        }

        public object ToObject()
        {
            var fields = this.group_fields.SelectMany(x => x.fields).ToList();
            if (!fields.Any()) return null;
            var paramObject = new ExpandoObject();
            var props = paramObject as IDictionary<string, object>;
            foreach (var f in fields)
            {
                if (!f.isIgnore)
                {
                    if (f.data_type.Equals("uniqueidentifier"))
                    {
                        Guid value;
                        if (Guid.TryParse(f.columnValue, out value))
                            props[f.field_name] = value;
                        else
                            props[f.field_name] = null;
                    }
                    else if (f.data_type.Equals("datetime") && string.IsNullOrEmpty(f.columnValue))
                    {
                        props[f.field_name] = null;
                    }
                    else if (f.data_type.Equals("decimal") && string.IsNullOrEmpty(f.columnValue))
                    {
                        props[f.field_name] = 0;
                    }
                    else if (f.data_type.Equals("bit"))
                    {
                        props[f.field_name] = string.IsNullOrEmpty(f.columnValue) ? null : (bool?)bool.Parse(f.columnValue);
                    }
                    else
                    {
                        props[f.field_name] = f.columnValue;
                    }
                }
            }
            return paramObject;
        }
        public List<T> ToObjectList<T>()
            where T : class
        {
            var listObj = new List<T>();
            foreach (var g in this.group_fields)
            {
                var paramObject = new ExpandoObject();
                var props = paramObject as IDictionary<string, object>;
                foreach (var f in g.fields)
                {
                    if (!f.isIgnore)
                    {
                        if (f.data_type.Equals("uniqueidentifier"))
                        {
                            Guid value;
                            if (Guid.TryParse(f.columnValue, out value))
                                props[f.field_name] = value;
                            else
                                props[f.field_name] = null;
                        }
                        else if (f.data_type.Equals("datetime") && string.IsNullOrEmpty(f.columnValue))
                        {
                            props[f.field_name] = null;
                        }
                        else if (f.data_type.Equals("bit"))
                        {
                            props[f.field_name] = string.IsNullOrEmpty(f.columnValue) ? null : (bool?)bool.Parse(f.columnValue);
                        }
                        else
                        {
                            props[f.field_name] = f.columnValue;
                        }
                    }
                }
                var someObject = this.ObjectFromDictionary<T>(props);
                listObj.Add(someObject);
            }
            return listObj;
        }
        private T ObjectFromDictionary<T>(IDictionary<string, object> dict)
            where T : class
        {
            Type type = typeof(T);
            T result = (T)Activator.CreateInstance(type);
            foreach (var item in dict)
            {
                type.GetProperty(item.Key).SetValue(result, item.Value, null);
            }
            return result;
        }
        private IDictionary<string, object> ObjectToDictionary<T>(T item)
            where T : class
        {
            Type myObjectType = item.GetType();
            IDictionary<string, object> dict = new Dictionary<string, object>();
            var indexer = new object[0];
            PropertyInfo[] properties = myObjectType.GetProperties();
            foreach (var info in properties)
            {
                var value = info.GetValue(item, indexer);
                dict.Add(info.Name, value);
            }
            return dict;
        }
    }

    public class viewGroup
    {
        public string group_table { get; set; }
        public string group_key { get; set; }
        public string group_column { get; set; }
        public string group_cd { get; set; }
        public string group_name { get; set; }
        /// <summary>
        /// dùng để check render dưới dạng grid
        /// </summary>
        public bool IsGridEditor { get; set; }
        internal bool group_expand { get; set; }
        public bool expand
        {
            get
            {
                return fields?.Count > 0 || group_expand ? true : group_expand;
            }
        }
        public List<viewField> fields { get; set; }
        public object ToDisplayObject()
        {
            if (!fields.Any()) return null;
            var paramObject = new ExpandoObject();
            var props = paramObject as IDictionary<string, object>;
            foreach (var f in fields)
            {
                if (!f.isIgnore)
                {
                    if (f.data_type.Equals("uniqueidentifier"))
                    {
                        Guid value;
                        if (Guid.TryParse(f.columnValue, out value))
                            props[f.columnDisplay] = value;
                        else
                            props[f.columnDisplay] = null;
                    }
                    else if (f.data_type.Equals("datetime") && string.IsNullOrEmpty(f.columnValue))
                    {
                        props[f.columnDisplay] = null;
                    }
                    else if (f.data_type.Equals("bit"))
                    {
                        props[f.columnDisplay] = string.IsNullOrEmpty(f.columnValue) ? null : (bool?)bool.Parse(f.columnValue);
                    }
                    else
                    {
                        props[f.columnDisplay] = f.columnValue;
                    }
                }
            }
            return paramObject;
        }
    }

    public class viewField
    {
        public string group_cd { get; set; }
        public string table_name { get; set; }
        public string field_name { get; set; }
        public string data_type { get; set; }
        public string columnLabel { get; set; }
        public string columnLabelE { get; set; }
        public string columnValue { get; set; }
        public string columnClass { get; set; }
        public string columnType { get; set; }
        public string columnObject { get; set; }
        public string columnTooltip { get; set; }
        public bool isSpecial { get; set; }
        public bool isRequire { get; set; }
        public bool isDisable { get; set; }
        public bool isVisiable { get; set; }
        public bool isEmpty { get; set; }
        public string columnDisplay { get; set; }
        public bool isIgnore { get; set; }
        public string maxLength { get; set; }
        public string table_relation { get; set; }        
    }

    public class viewGridFlex
    {
        public string columnField { get; set; }
        public string columnCaption { get; set; }
        public int columnWidth { get; set; }
        public string fieldType { get; set; }
        internal string columnClass { get; set; }
        public List<string> cellClass
        {
            get
            {
                if (this.columnClass != null)
                    return this.columnClass.Split(',').ToList();
                else
                    return null;
            }
        }
        public string Pinned { get; set; }
        public bool isMasterDetail { get; set; }
        public bool isStatusLable { get; set; }
        public bool isHide { get; set; }
        public bool isFilter { get; set; }
        public int ordinal { get; set; }
        //Config header group
        public string group_cd { get; set; }
        public string group_name { get; set; }
        public string columnObject { get; set; }
        public List<viewGridFlex> Children { get; set; }
    }
    public enum WorkStatus
    {
        [Description("To do")]
        ToDo = 0,
        [Description("In progress")]
        Doing = 1,
        [Description("Success")]
        Done = 2,
        [Description("Transiton to")]
        UnDo = 3
    }
}