﻿


using UNI.Common.CommonBase;
using UNI.Model;
using Uni.Personal.DAL.Interfaces;
using Uni.Personal.Model;

namespace Uni.Personal.DAL.Repositories
{
    /// <summary>
    /// Order repository implementation
    /// </summary>
    public class CustomerRepository : UniBaseRepository, ICustomerRepository
    {
        public CustomerRepository(IUniCommonBaseRepository commonBase) : base(commonBase)
        {
        }

        /// <summary>
        /// Get paginated list of orders
        /// </summary>
        /// <param name="filter">Filter criteria</param>
        /// <returns>Paginated list of orders</returns>
        public async Task<CommonListPage> GetPageAsync(CustomerFilterInput filter)
        {
            // Call stored procedure to get paginated orders
            return await GetPageAsync("sp_customer_page", filter, param =>
            {
                // Add custom parameters if needed
                // param.Add("@OrderCode", filter.OrderCode);
                // param.Add("@CustomerName", filter.CustomerName);
                // param.Add("@FromDate", filter.FromDate);
                // param.Add("@ToDate", filter.ToDate);
                param.Add("@Status", filter.code);
                // param.Add("@ProductType", filter.ProductType);
                return param;
            });
        }

        /// <summary>
        /// Get order information by ID
        /// </summary>
        /// <param name="oid">Order ID</param>
        /// <returns>Order information</returns>
        public async Task<CommonViewOidInfo> GetInfoAsync(Guid? oid)
        {
            // Call stored procedure to get order info
            return await GetFieldsAsync<CommonViewOidInfo>("sp_personal_customer_fields", new { Oid = oid });
        }

        /// <summary>
        /// Create or update order
        /// </summary>
        /// <param name="info">Order information</param>
        /// <returns>Validation result with order ID</returns>
        public async Task<BaseValidate<Guid?>> SetInfoAsync(CommonViewOidInfo info)
        {
            // Call stored procedure to create/update order
            return await SetInfoAsync<BaseValidate<Guid?>>("sp_personal_customer_set", info, new { info.Oid });
        }

        /// <summary>
        /// Delete order
        /// </summary>
        /// <param name="oid">Order ID</param>
        /// <returns>Validation result</returns>
        public async Task<BaseValidate> DeleteAsync(Guid? oid)
        {
            // Call stored procedure to delete order
            return await DeleteAsync("sp_customer_del", new { Oid = oid });
        }

    }
}
