using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using UNI.Utilities.HttpClientExtension.Models;

namespace UNI.Utilities.HttpClientExtension
{
    public static class HttpclientPostExtension
    {
        public static Task<ResponseMessage<T?>> PostJsonAsync<T>(this HttpClient client, string url,object? data, CancellationToken cancellationToken = default)
        {
            var content = HttpclientExtension.CreateJsonContent(data);
            var requestMessage = new HttpRequestMessage(HttpMethod.Post, url)
            {
                Content = content
            };
            return client.RequestAsync<T>(requestMessage, cancellationToken);
        }
    }
}