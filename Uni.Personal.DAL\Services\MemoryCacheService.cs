using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Text.RegularExpressions;
using Uni.Personal.DAL.Interfaces;

namespace Uni.Personal.DAL.Services
{
    /// <summary>
    /// In-memory cache service implementation for development/testing
    /// </summary>
    public class MemoryCacheService : ICacheService
    {
        private readonly IMemoryCache _memoryCache;
        private readonly ILogger<MemoryCacheService> _logger;
        private readonly ConcurrentDictionary<string, bool> _keyTracker;

        public MemoryCacheService(IMemoryCache memoryCache, ILogger<MemoryCacheService> logger)
        {
            _memoryCache = memoryCache;
            _logger = logger;
            _keyTracker = new ConcurrentDictionary<string, bool>();
        }

        /// <inheritdoc />
        public Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default)
        {
            try
            {
                var value = _memoryCache.Get<T>(key);
                return Task.FromResult(value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cached value for key: {Key}", key);
                return Task.FromResult<T?>(default);
            }
        }

        /// <inheritdoc />
        public Task SetAsync<T>(string key, T value, TimeSpan? expiration = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var options = new MemoryCacheEntryOptions();
                
                if (expiration.HasValue)
                {
                    options.SetAbsoluteExpiration(expiration.Value);
                }
                else
                {
                    options.SetAbsoluteExpiration(TimeSpan.FromHours(1));
                }

                // Add removal callback to track keys
                options.RegisterPostEvictionCallback((k, v, reason, state) =>
                {
                    _keyTracker.TryRemove(k.ToString()!, out _);
                });

                _memoryCache.Set(key, value, options);
                _keyTracker.TryAdd(key, true);
                
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting cached value for key: {Key}", key);
                throw;
            }
        }

        /// <inheritdoc />
        public Task RemoveAsync(string key, CancellationToken cancellationToken = default)
        {
            try
            {
                _memoryCache.Remove(key);
                _keyTracker.TryRemove(key, out _);
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing cached value for key: {Key}", key);
                throw;
            }
        }

        /// <inheritdoc />
        public Task RemoveAsync(IEnumerable<string> keys, CancellationToken cancellationToken = default)
        {
            try
            {
                foreach (var key in keys)
                {
                    _memoryCache.Remove(key);
                    _keyTracker.TryRemove(key, out _);
                }
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing multiple cached values");
                throw;
            }
        }

        /// <inheritdoc />
        public Task RemoveByPatternAsync(string pattern, CancellationToken cancellationToken = default)
        {
            try
            {
                // Convert Redis pattern to regex
                var regexPattern = pattern.Replace("*", ".*").Replace("?", ".");
                var regex = new Regex($"^{regexPattern}$", RegexOptions.IgnoreCase);

                var keysToRemove = _keyTracker.Keys.Where(key => regex.IsMatch(key)).ToList();
                
                foreach (var key in keysToRemove)
                {
                    _memoryCache.Remove(key);
                    _keyTracker.TryRemove(key, out _);
                }

                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing cached values by pattern: {Pattern}", pattern);
                throw;
            }
        }

        /// <inheritdoc />
        public Task<bool> ExistsAsync(string key, CancellationToken cancellationToken = default)
        {
            try
            {
                var exists = _memoryCache.TryGetValue(key, out _);
                return Task.FromResult(exists);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if key exists: {Key}", key);
                return Task.FromResult(false);
            }
        }

        /// <inheritdoc />
        public async Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiration = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var cachedValue = await GetAsync<T>(key, cancellationToken);
                if (cachedValue != null)
                {
                    return cachedValue;
                }

                var newValue = await factory();
                await SetAsync(key, newValue, expiration, cancellationToken);
                return newValue;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetOrSetAsync for key: {Key}", key);
                throw;
            }
        }

        /// <inheritdoc />
        public Task<Dictionary<string, T?>> GetManyAsync<T>(IEnumerable<string> keys, CancellationToken cancellationToken = default)
        {
            try
            {
                var result = new Dictionary<string, T?>();

                foreach (var key in keys)
                {
                    if (_memoryCache.TryGetValue<T>(key, out var value))
                    {
                        result[key] = value;
                    }
                }

                return Task.FromResult(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting multiple cached values");
                throw;
            }
        }

        /// <inheritdoc />
        public Task SetManyAsync<T>(Dictionary<string, T> keyValuePairs, TimeSpan? expiration = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var exp = expiration ?? TimeSpan.FromHours(1);
                var options = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = exp
                };

                options.RegisterPostEvictionCallback((k, v, reason, state) =>
                {
                    _keyTracker.TryRemove(k.ToString()!, out _);
                });

                foreach (var kvp in keyValuePairs)
                {
                    _memoryCache.Set(kvp.Key, kvp.Value, options);
                    _keyTracker.TryAdd(kvp.Key, true);
                }

                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting multiple cached values");
                throw;
            }
        }

        /// <inheritdoc />
        public Task RefreshAsync(string key, TimeSpan? expiration = null, CancellationToken cancellationToken = default)
        {
            try
            {
                if (_memoryCache.TryGetValue(key, out var value))
                {
                    // Re-set the value with new expiration
                    var exp = expiration ?? TimeSpan.FromHours(1);
                    var options = new MemoryCacheEntryOptions
                    {
                        AbsoluteExpirationRelativeToNow = exp
                    };

                    options.RegisterPostEvictionCallback((k, v, reason, state) =>
                    {
                        _keyTracker.TryRemove(k.ToString()!, out _);
                    });

                    _memoryCache.Set(key, value, options);
                }

                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing cache expiration for key: {Key}", key);
                throw;
            }
        }

        /// <inheritdoc />
        public Task<IEnumerable<string>> GetKeysAsync(string pattern = "*", CancellationToken cancellationToken = default)
        {
            try
            {
                var regexPattern = pattern.Replace("*", ".*").Replace("?", ".");
                var regex = new Regex($"^{regexPattern}$", RegexOptions.IgnoreCase);

                var matchingKeys = _keyTracker.Keys.Where(key => regex.IsMatch(key));
                return Task.FromResult(matchingKeys);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting keys by pattern: {Pattern}", pattern);
                throw;
            }
        }

        /// <inheritdoc />
        public Task ClearAllAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                var keysToRemove = _keyTracker.Keys.ToList();
                foreach (var key in keysToRemove)
                {
                    _memoryCache.Remove(key);
                    _keyTracker.TryRemove(key, out _);
                }

                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing all cache");
                throw;
            }
        }

        /// <inheritdoc />
        public Task<CacheStatistics?> GetStatisticsAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                var stats = new CacheStatistics
                {
                    TotalKeys = _keyTracker.Count
                };

                return Task.FromResult<CacheStatistics?>(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cache statistics");
                return Task.FromResult<CacheStatistics?>(null);
            }
        }
    }
}
