using UNI.Model;
using Uni.Personal.Model;
using Uni.Personal.Model.AttachmentModels;

namespace Uni.Personal.BLL.Interfaces
{
    /// <summary>
    /// Attachment service interface
    /// </summary>
    public interface IAttachmentService
    {
        Task<IEnumerable<AttachmentInfo>> GetAttachmentsAsync(Guid? groupField);
        Task<AttachmentSet?> SetAttachmentsAsync(AttachmentSet attachment);

    }
}
