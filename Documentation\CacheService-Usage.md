# General-Purpose Cache Service Usage

## Overview

The `ICacheService` provides a comprehensive, general-purpose distributed caching solution that works with both Redis (production) and in-memory cache (development).

## Basic Usage

### Dependency Injection

```csharp
public class YourService
{
    private readonly ICacheService _cache;

    public YourService(ICacheService cache)
    {
        _cache = cache;
    }
}
```

### Simple Get/Set Operations

```csharp
// Set a value with default expiration (1 hour)
await _cache.SetAsync("user:123", userData);

// Set a value with custom expiration
await _cache.SetAsync("session:abc", sessionData, TimeSpan.FromMinutes(30));

// Get a value
var user = await _cache.GetAsync<UserData>("user:123");

// Check if key exists
var exists = await _cache.ExistsAsync("user:123");

// Remove a value
await _cache.RemoveAsync("user:123");
```

### Get-or-Set Pattern

```csharp
// Get from cache or fetch from database if not cached
var userData = await _cache.GetOrSetAsync(
    "user:123",
    async () => await _userRepository.GetByIdAsync(123),
    TimeSpan.FromMinutes(30)
);
```

### Batch Operations

```csharp
// Get multiple values
var keys = new[] { "user:1", "user:2", "user:3" };
var users = await _cache.GetManyAsync<UserData>(keys);

// Set multiple values
var userDict = new Dictionary<string, UserData>
{
    ["user:1"] = user1,
    ["user:2"] = user2,
    ["user:3"] = user3
};
await _cache.SetManyAsync(userDict, TimeSpan.FromHours(1));

// Remove multiple values
await _cache.RemoveAsync(keys);
```

### Pattern-Based Operations

```csharp
// Remove all user cache entries
await _cache.RemoveByPatternAsync("user:*");

// Get all keys matching pattern
var userKeys = await _cache.GetKeysAsync("user:*");

// Clear all cache
await _cache.ClearAllAsync();
```

### Cache Management

```csharp
// Refresh expiration time
await _cache.RefreshAsync("user:123", TimeSpan.FromHours(2));

// Get cache statistics
var stats = await _cache.GetStatisticsAsync();
if (stats != null)
{
    Console.WriteLine($"Total keys: {stats.TotalKeys}");
    Console.WriteLine($"Memory usage: {stats.MemoryUsage} bytes");
    Console.WriteLine($"Hit ratio: {stats.HitRatio:P}");
}
```

## Common Patterns

### Repository Pattern with Caching

```csharp
public class UserService
{
    private readonly IUserRepository _repository;
    private readonly ICacheService _cache;

    public UserService(IUserRepository repository, ICacheService cache)
    {
        _repository = repository;
        _cache = cache;
    }

    public async Task<User> GetUserAsync(int userId)
    {
        return await _cache.GetOrSetAsync(
            $"user:{userId}",
            () => _repository.GetByIdAsync(userId),
            TimeSpan.FromMinutes(30)
        );
    }

    public async Task<User> UpdateUserAsync(User user)
    {
        var updatedUser = await _repository.UpdateAsync(user);
        
        // Invalidate cache
        await _cache.RemoveAsync($"user:{user.Id}");
        
        return updatedUser;
    }

    public async Task<List<User>> GetUsersByDepartmentAsync(int departmentId)
    {
        return await _cache.GetOrSetAsync(
            $"users:department:{departmentId}",
            () => _repository.GetByDepartmentAsync(departmentId),
            TimeSpan.FromMinutes(15)
        );
    }
}
```

### Paginated Data Caching

```csharp
public async Task<PagedResult<Order>> GetOrdersAsync(int page, int pageSize, string filter)
{
    var cacheKey = $"orders:page:{page}:size:{pageSize}:filter:{filter?.GetHashCode()}";
    
    return await _cache.GetOrSetAsync(
        cacheKey,
        async () => await _orderRepository.GetPagedAsync(page, pageSize, filter),
        TimeSpan.FromMinutes(5)
    );
}
```

### Session Caching

```csharp
public class SessionService
{
    private readonly ICacheService _cache;

    public async Task<SessionData> GetSessionAsync(string sessionId)
    {
        return await _cache.GetAsync<SessionData>($"session:{sessionId}");
    }

    public async Task SetSessionAsync(string sessionId, SessionData data)
    {
        await _cache.SetAsync($"session:{sessionId}", data, TimeSpan.FromMinutes(20));
    }

    public async Task ExtendSessionAsync(string sessionId)
    {
        await _cache.RefreshAsync($"session:{sessionId}", TimeSpan.FromMinutes(20));
    }
}
```

### Configuration Caching

```csharp
public class ConfigurationService
{
    private readonly ICacheService _cache;
    private readonly IConfigRepository _repository;

    public async Task<AppConfig> GetConfigAsync()
    {
        return await _cache.GetOrSetAsync(
            "app:config",
            () => _repository.GetConfigAsync(),
            TimeSpan.FromHours(1)
        );
    }

    public async Task InvalidateConfigAsync()
    {
        await _cache.RemoveAsync("app:config");
    }
}
```

## Configuration

### Development (In-Memory Cache)
```json
{
  "Cache": {
    "ConnectionString": "localhost:6379",
    "KeyPrefix": "uni_personal_dev:"
  }
}
```

### Production (Redis)
```json
{
  "Cache": {
    "ConnectionString": "your-redis-server:6379",
    "KeyPrefix": "uni_personal:",
    "ConnectTimeoutSeconds": 30,
    "CommandTimeoutSeconds": 30,
    "EnableRetry": true,
    "MaxRetryAttempts": 3
  }
}
```

## Best Practices

1. **Use meaningful cache keys**: Include entity type and ID
2. **Set appropriate expiration times**: Balance between performance and data freshness
3. **Handle cache misses gracefully**: Always have a fallback to the data source
4. **Invalidate related cache**: When updating data, remove related cache entries
5. **Use batch operations**: When working with multiple items
6. **Monitor cache performance**: Use statistics to optimize cache usage
7. **Consider cache warming**: Pre-populate frequently accessed data

## Error Handling

The cache service is designed to be resilient. If cache operations fail, they will log errors but not break your application flow. Always implement fallback logic:

```csharp
public async Task<User> GetUserAsync(int userId)
{
    try
    {
        // Try cache first
        var cached = await _cache.GetAsync<User>($"user:{userId}");
        if (cached != null) return cached;
    }
    catch (Exception ex)
    {
        _logger.LogWarning(ex, "Cache get failed for user {UserId}", userId);
    }

    // Fallback to database
    var user = await _repository.GetByIdAsync(userId);
    
    try
    {
        // Try to cache for next time
        await _cache.SetAsync($"user:{userId}", user, TimeSpan.FromMinutes(30));
    }
    catch (Exception ex)
    {
        _logger.LogWarning(ex, "Cache set failed for user {UserId}", userId);
    }

    return user;
}
```
