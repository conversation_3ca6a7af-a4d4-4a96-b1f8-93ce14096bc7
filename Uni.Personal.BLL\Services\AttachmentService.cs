using UNI.Model;
using Uni.Personal.BLL.Interfaces;
using Uni.Personal.DAL.Interfaces;
using Uni.Personal.Model;
using Uni.Personal.Model.AttachmentModels;

namespace Uni.Personal.BLL.Services
{
    /// <summary>
    /// Attachment service implementation
    /// </summary>
    public class AttachmentService : IAttachmentService
    {
        private readonly IAttachmentRepository _attachmentRepository;

        public AttachmentService(IAttachmentRepository attachmentRepository)
        {
            _attachmentRepository = attachmentRepository;
        }

        public Task<IEnumerable<AttachmentInfo>> GetAttachmentsAsync(Guid? groupField)
        {
            return _attachmentRepository.GetAttachmentsAsync(groupField);
        }

        public Task<AttachmentSet?> SetAttachmentsAsync(AttachmentSet attachment)
        {
            attachment.Attachments?.ForEach(x => x.Id = x.Id ?? Guid.NewGuid());
            return _attachmentRepository.SetAttachmentsAsync(attachment);
        }

    }
}
